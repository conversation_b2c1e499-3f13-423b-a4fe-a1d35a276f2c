# 🎯 ScalpWizard - Hệ Thống Vào Lệnh

## ⚠️ **QUAN TRỌNG: Bot KHÔNG Tự Động Trade**

**ScalpWizard chỉ phân tích và gửi tín hiệu, KHÔNG tự động đặt lệnh trên sàn.**

Bot hoạt động như một **Signal Provider**:
- ✅ Phân tích kỹ thuật real-time
- ✅ Gửi tín hiệu qua Telegram
- ✅ Theo dõi SL/TP dựa trên giá thị trường
- ❌ KHÔNG đặt lệnh thực tế trên Binance

## 🔍 **Quy Trình Phân Tích Tín Hiệu**

### **1. Thu Thập Dữ Liệu**
```
Binance WebSocket → 30 coins × 3 timeframes → 90 streams real-time
```

### **2. T<PERSON>h Toán Chỉ Báo**
Mỗi khi có nến mới đóng:
```javascript
// Tính toán các chỉ báo
const indicators = {
  ema50: calculateEMA(closes, 50),
  ema200: calculateEMA(closes, 200), 
  macd: calculateMACD(closes),
  rsi: calculateRSI(closes),
  engulfing: checkEngulfingPattern(candles)
};
```

### **3. Kiểm Tra Điều Kiện Vào Lệnh**

## 📈 **Điều Kiện BUY (Tất Cả 5 Điều Kiện Phải Đúng)**

### **1. Giá > EMA200**
```javascript
currentPrice > ema200  // Xu hướng tăng
```

### **2. EMA50 > EMA200**  
```javascript
ema50 > ema200  // Trend alignment
```

### **3. MACD Crossover Bullish**
```javascript
// MACD vừa cắt lên Signal line
prevDiff = prevMACD - prevSignal <= 0
currentDiff = currentMACD - currentSignal > 0
```

### **4. RSI trong Vùng Mua**
```javascript
rsi > 50 && rsi >= 55 && rsi <= 65  // Vùng 55-65
```

### **5. Bullish Engulfing Pattern**
```javascript
// Nến trước: đỏ (close < open)
// Nến hiện tại: xanh (close > open)  
// Nến hiện tại "nuốt chửng" nến trước
current.open < prev.close && current.close > prev.open
```

## 📉 **Điều Kiện SELL (Tất Cả 5 Điều Kiện Phải Đúng)**

### **1. Giá < EMA200**
```javascript
currentPrice < ema200  // Xu hướng giảm
```

### **2. EMA50 < EMA200**
```javascript
ema50 < ema200  // Trend alignment
```

### **3. MACD Crossover Bearish**
```javascript
// MACD vừa cắt xuống Signal line
prevDiff = prevMACD - prevSignal >= 0
currentDiff = currentMACD - currentSignal < 0
```

### **4. RSI trong Vùng Bán**
```javascript
rsi < 50 && rsi >= 35 && rsi <= 45  // Vùng 35-45
```

### **5. Bearish Engulfing Pattern**
```javascript
// Nến trước: xanh (close > open)
// Nến hiện tại: đỏ (close < open)
// Nến hiện tại "nuốt chửng" nến trước  
current.open > prev.close && current.close < prev.open
```

## 🔄 **Luồng Xử Lý Signal**

### **Step 1: Real-time Analysis**
```javascript
// Mỗi khi nến đóng
onCandleClose(candleData) {
  // 1. Tính toán indicators
  const indicators = calculateAllIndicators(candles);
  
  // 2. Kiểm tra MACD crossover
  const macdCrossover = checkMACDCrossover(indicators.macd, candles);
  
  // 3. Kiểm tra điều kiện BUY
  const buySignal = checkBuyConditions(indicators, macdCrossover);
  
  // 4. Kiểm tra điều kiện SELL  
  const sellSignal = checkSellConditions(indicators, macdCrossover);
}
```

### **Step 2: Signal Validation**
```javascript
// Tất cả điều kiện phải = true
const isValid = Object.values(conditions).every(condition => condition === true);

if (isValid) {
  createSignal(symbol, timeframe, type, indicators, candles);
}
```

### **Step 3: Signal Creation**
```javascript
const signal = {
  symbol: 'BTCUSDT',
  timeframe: '5m', 
  type: 'BUY',
  entry: 43250.50,           // Giá hiện tại
  stopLoss: 43000.00,        // Từ support/resistance
  takeProfit: 43682.50,      // 1% từ entry
  indicators: { ... },        // Tất cả chỉ báo
  marketData: { ... }         // Dữ liệu nến
};
```

### **Step 4: Duplicate Check**
```javascript
// Kiểm tra signal trùng lặp trong 1 giờ
const existingSignal = await TradingSignal.findOne({
  symbol,
  timeframe, 
  type,
  status: 'active',
  createdAt: { $gte: oneHourAgo }
});

if (existingSignal) {
  return null; // Bỏ qua signal trùng
}
```

### **Step 5: Save & Notify**
```javascript
// 1. Lưu vào database
const savedSignal = await signal.save();

// 2. Gửi Telegram
await telegramBot.sendSignalNotification(savedSignal);

// 3. Thêm vào order manager để theo dõi
orderManager.addSignalToMonitoring(savedSignal);
```

## 📱 **Thông Báo Telegram**

### **Format Tín Hiệu:**
```
🚀 TÍNH HIỆU TRADING 🚀

📊 Cặp: BTCUSDT
⏰ Thời gian: 28/10/2024 10:30:15
📈 Loại lệnh: 📈 MUA  
💰 Entry: 43250.500000
🛑 Stop Loss: 43000.000000
🎯 Take Profit: 43682.500000

📋 Chỉ báo:
📊 EMA50: 43200.000000
📊 EMA200: 43000.000000  
📊 MACD: 0.500000
📊 Signal: 0.300000
📊 RSI: 62.00
📊 Pattern: 🟢 Bullish Engulfing

#ScalpWizard #BTCUSDT
```

## 🎯 **Ví Dụ Thực Tế**

### **Scenario BUY Signal:**
```
Symbol: BTCUSDT
Timeframe: 5m
Current Price: 43,250

Conditions Check:
✅ Price > EMA200: 43,250 > 43,000
✅ EMA50 > EMA200: 43,200 > 43,000  
✅ MACD Crossover: Bullish (0.5 > 0.3)
✅ RSI Zone: 62 (trong vùng 55-65)
✅ Engulfing: Bullish pattern detected

→ SIGNAL GENERATED: BUY
→ Entry: 43,250
→ SL: 43,000 (support level)
→ TP: 43,682.50 (1% từ entry)
```

### **Scenario No Signal:**
```
Symbol: ETHUSDT  
Timeframe: 15m
Current Price: 4,100

Conditions Check:
✅ Price > EMA200: 4,100 > 4,050
✅ EMA50 > EMA200: 4,080 > 4,050
❌ MACD Crossover: None (no crossover)
✅ RSI Zone: 58 (trong vùng 55-65)
❌ Engulfing: None

→ NO SIGNAL (chỉ 3/5 điều kiện đúng)
```

## 📊 **Tần Suất Tín Hiệu**

### **Độ Khó Của Điều Kiện:**
- **Rất nghiêm ngặt**: Tất cả 5 điều kiện phải đúng
- **MACD Crossover**: Chỉ xảy ra khi có đảo chiều
- **Engulfing Pattern**: Cần 2 nến liên tiếp đặc biệt
- **RSI Zone**: Vùng hẹp (55-65 cho BUY, 35-45 cho SELL)

### **Tần Suất Dự Kiến:**
- **1m timeframe**: 5-10 signals/ngày per coin
- **5m timeframe**: 2-5 signals/ngày per coin  
- **15m timeframe**: 1-3 signals/ngày per coin
- **30 coins total**: 50-200 signals/ngày

## 🔄 **Theo Dõi Sau Signal**

### **Virtual Order Tracking:**
```javascript
// Bot theo dõi giá real-time
setInterval(() => {
  const currentPrice = await getCurrentPrice(symbol);
  
  // Check SL
  if (signal.checkStopLoss(currentPrice)) {
    updateSignalStatus('hit_sl', currentPrice);
    sendResultNotification('LOSS');
  }
  
  // Check TP  
  if (signal.checkTakeProfit(currentPrice)) {
    updateSignalStatus('hit_tp', currentPrice);
    sendResultNotification('WIN');
  }
}, 5000); // Mỗi 5 giây
```

## ⚙️ **Cấu Hình Điều Kiện**

### **RSI Zones:**
```json
{
  "rsi": {
    "buyZone": [55, 65],   // Có thể điều chỉnh
    "sellZone": [35, 45]   // Có thể điều chỉnh
  }
}
```

### **MACD Settings:**
```json
{
  "macd": {
    "fast": 12,    // EMA nhanh
    "slow": 26,    // EMA chậm  
    "signal": 9    // Signal line
  }
}
```

## 🎯 **Kết Luận**

### **Bot ScalpWizard:**
- ✅ **Signal Provider**: Chỉ phân tích và gửi tín hiệu
- ✅ **Nghiêm ngặt**: 5 điều kiện phải đúng hết
- ✅ **Real-time**: Phân tích liên tục 24/7
- ✅ **Intelligent**: Sử dụng multiple indicators
- ❌ **KHÔNG Trade**: User phải tự vào lệnh

### **Cách Sử Dụng:**
1. **Nhận signal** qua Telegram
2. **Kiểm tra** thông tin Entry/SL/TP
3. **Tự quyết định** có vào lệnh không
4. **Đặt lệnh** thủ công trên sàn
5. **Theo dõi** kết quả qua bot

**Bot là công cụ hỗ trợ phân tích, quyết định cuối cùng thuộc về trader! 📊🎯**
