# 📝 ScalpWizard - Changelog

## [v1.1.0] - 2025-10-28

### ✨ New Features
- **Added 1m timeframe support**
  - Bot now supports 1m, 5m, and 15m timeframes
  - More frequent signal analysis for scalping
  - Better entry opportunities

### 🔧 Technical Changes

#### Configuration Updates
- `config/default.json`: Added "1m" to timeframes array
- Default timeframes: `["1m", "5m", "15m"]`

#### Database Schema Updates
- `lib/models/tradingSignal.js`: Added "1m" to enum validation
- `lib/models/marketData.js`: Added "1m" to enum validation

#### Documentation Updates
- `README.md`: Updated timeframes information
- `DOCS.md`: Updated configuration examples
- `QUICKSTART.md`: Updated timeframe examples
- `SUMMARY.md`: Updated feature list

### 📊 Impact

#### Performance Considerations
- **1m timeframe** generates more data and signals
- Increased WebSocket connections: 30 coins × 3 timeframes = 90 connections
- More frequent analysis and potential signals

#### Resource Usage
- **Memory**: Slightly increased due to more data storage
- **CPU**: More frequent indicator calculations
- **Network**: More WebSocket connections to Binance

#### Signal Quality
- **Pros**: More entry opportunities, faster signals
- **Cons**: Potentially more noise, need careful filtering

### 🎯 Usage

#### Enable 1m Timeframe
The 1m timeframe is now enabled by default. To customize:

```json
{
  "trading": {
    "timeframes": ["1m", "5m", "15m"]
  }
}
```

#### Disable 1m if Needed
If you want to reduce resource usage:

```json
{
  "trading": {
    "timeframes": ["5m", "15m"]
  }
}
```

### 🧪 Testing
- ✅ All tests pass with 1m timeframe
- ✅ Database schema validation works
- ✅ WebSocket connections stable
- ✅ Signal analysis functions correctly

### 📈 Expected Results

#### More Signals
- 1m timeframe will generate more frequent signals
- Faster reaction to market movements
- Better for scalping strategies

#### Resource Monitoring
Monitor these metrics when using 1m:
- Memory usage
- CPU utilization  
- WebSocket connection stability
- Database storage growth

### 🚨 Important Notes

#### Scalping Strategy
- 1m timeframe is ideal for scalping
- Requires faster decision making
- Higher frequency of signals

#### Risk Management
- Same SL/TP rules apply
- Consider tighter risk management for 1m
- Monitor win rate carefully

#### Performance Tips
- Monitor server resources
- Consider reducing `maxCoinsToTrack` if needed
- Use SSD for database storage

### 🔄 Migration

#### Existing Users
No migration needed. The change is backward compatible:
- Existing signals remain valid
- Old timeframes still work
- No database migration required

#### New Installations
1m timeframe is enabled by default in new installations.

### 📊 Monitoring

#### Key Metrics to Watch
- Signal frequency per timeframe
- Win rate by timeframe
- Resource utilization
- WebSocket connection health

#### Logs to Monitor
```bash
# Signal generation
grep "Signal" logs/system-*.log

# WebSocket connections
grep "WebSocket" logs/system-*.log

# Performance issues
grep "ERROR\|WARN" logs/system-*.log
```

### 🎉 Summary

ScalpWizard now supports **1m, 5m, and 15m** timeframes, providing:
- ✅ More trading opportunities
- ✅ Faster market reaction
- ✅ Better scalping capabilities
- ✅ Flexible timeframe configuration

**Ready for enhanced scalping trading! 🚀📈**

---

## [v1.0.0] - 2025-10-28

### 🎉 Initial Release
- Complete trading bot with technical analysis
- Telegram notifications
- MongoDB integration
- Real-time WebSocket data
- Comprehensive documentation

### Core Features
- EMA, MACD, RSI, Engulfing pattern analysis
- Automatic SL/TP management
- Win/loss statistics
- 24/7 operation
- Error handling and recovery
