# ScalpWizard - <PERSON>t Trading Tự Động

Bo<PERSON> trading tự động phân tích tín hiệu kỹ thuật cho Binance Futures và gửi thông báo qua Telegram.

## 🎯 Tính Năng Chính

### 📊 Phân Tích Kỹ Thuật
- **EMA (50, 200)**: <PERSON><PERSON><PERSON> định xu hướng thị trường
- **MACD (12,26,9)**: <PERSON><PERSON>t hiện tín hiệu crossover
- **RSI (14)**: Đ<PERSON>h giá vùng quá mua/quá bán
- **Engulfing Pattern**: Nhận diện mô hình nến đảo chiều

### 🚀 Tín Hiệu Trading
- **Điều kiện BUY**: Giá > EMA200, EMA50 > EMA200, MACD cắt lên, RSI 55-65, Bullish Engulfing
- **Điều kiện SELL**: <PERSON><PERSON><PERSON> < EMA200, EMA50 < EMA200, MACD cắt xuống, RSI 35-45, Bearish Engulfing
- **Timeframes**: 1m, 5m, 15m
- **Top 30 coins** volume cao nhất (tự động cập nhật mỗi giờ)

### 📱 Thông Báo Telegram
- Tín hiệu vào lệnh với đầy đủ thông tin Entry, SL, TP
- Kết quả lệnh khi chạm SL/TP
- Thống kê win/loss rate
- Báo cáo hệ thống định kỳ

### 📈 Quản Lý Rủi Ro
- **Stop Loss**: 0.5% hoặc tại support/resistance
- **Take Profit**: 1-2% hoặc theo tỷ lệ RR 1:1.5-1:2
- Theo dõi real-time và tự động cập nhật kết quả

## 🛠️ Cài Đặt

### 1. Yêu Cầu Hệ Thống
- Node.js >= 16.0.0
- MongoDB
- Redis (optional)
- Telegram Bot Token

### 2. Clone Repository
```bash
git clone <repository-url>
cd ScalpWizard
```

### 3. Cài Đặt Dependencies
```bash
npm install
```

### 4. Cấu Hình Environment
```bash
cp .env.example .env
```

Chỉnh sửa file `.env`:
```env
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_CHAT_ID=your_chat_id_here
MONGO_HOST=localhost
MONGO_PORT=27017
MONGO_DATABASE=scalpwizard
```

### 5. Cấu Hình Trading
Chỉnh sửa `config/default.json` để tùy chỉnh:
- Timeframes
- Chỉ báo kỹ thuật
- Quản lý rủi ro
- Thông báo Telegram

## 🚀 Chạy Bot

### Development
```bash
npm run dev
```

### Production
```bash
npm start
```

## 📊 API Endpoints

### Trạng Thái Hệ Thống
```
GET /api/v1/trading/status
```

### Thống Kê Trading
```
GET /api/v1/trading/statistics?days=30
```

### Signals Gần Đây
```
GET /api/v1/trading/signals/recent?limit=10
```

### Health Check
```
GET /health
```

## 📁 Cấu Trúc Project

```
ScalpWizard/
├── lib/
│   ├── trading/
│   │   ├── binanceClient.js      # Kết nối Binance API/WebSocket
│   │   ├── indicators.js         # Tính toán chỉ báo kỹ thuật
│   │   ├── signalAnalyzer.js     # Phân tích tín hiệu
│   │   ├── orderManager.js       # Quản lý lệnh và SL/TP
│   │   ├── telegramBot.js        # Gửi thông báo Telegram
│   │   └── scheduler.js          # Tác vụ định kỳ
│   ├── services/
│   │   ├── marketDataService.js  # Quản lý dữ liệu thị trường
│   │   └── signalService.js      # Xử lý logic tín hiệu
│   └── models/
│       ├── tradingSignal.js      # Model tín hiệu trading
│       └── marketData.js         # Model dữ liệu thị trường
├── config/
│   └── default.json              # Cấu hình chính
├── logs/                         # File log
└── README.md
```

## ⚙️ Cấu Hình Chi Tiết

### Trading Settings
```json
{
  "trading": {
    "timeframes": ["1m", "5m", "15m"],
    "indicators": {
      "ema": { "fast": 50, "slow": 200 },
      "macd": { "fast": 12, "slow": 26, "signal": 9 },
      "rsi": { "period": 14, "buyZone": [55, 65], "sellZone": [35, 45] }
    },
    "riskManagement": {
      "stopLossPercent": 0.5,
      "takeProfitPercent": [1, 2]
    }
  }
}
```

### Telegram Settings
```json
{
  "telegram": {
    "botToken": "your_bot_token",
    "chatId": "your_chat_id",
    "enabled": true
  }
}
```

## 📈 Cách Hoạt Động

### 1. Thu Thập Dữ Liệu
- Lấy top 30 coins volume cao nhất từ Binance
- Kết nối WebSocket để nhận dữ liệu real-time
- Lưu trữ dữ liệu nến vào MongoDB

### 2. Phân Tích Tín Hiệu
- Tính toán chỉ báo kỹ thuật cho mỗi nến mới
- Kiểm tra điều kiện vào lệnh BUY/SELL
- Tạo tín hiệu nếu đủ điều kiện

### 3. Gửi Thông Báo
- Format thông báo với đầy đủ thông tin
- Gửi qua Telegram Bot
- Lưu tín hiệu vào database

### 4. Theo Dõi Lệnh
- Monitor giá real-time
- Kiểm tra SL/TP
- Cập nhật kết quả và gửi thông báo

## 📊 Thống Kê & Báo Cáo

### Thống Kê Tự Động
- Win/Loss ratio
- P&L tổng và trung bình
- Performance theo từng coin
- Báo cáo hàng ngày

### Log Files
- System logs: `logs/system-*.log`
- Statistics backup: `logs/statistics-backup-*.json`
- Error logs với stack trace đầy đủ

## 🔧 Troubleshooting

### Lỗi Kết Nối
```bash
# Kiểm tra MongoDB
mongosh mongodb://localhost:27017/scalpwizard

# Kiểm tra Redis
redis-cli ping

# Test Telegram Bot
curl -X GET "https://api.telegram.org/bot<TOKEN>/getMe"
```

### Lỗi WebSocket
- Kiểm tra kết nối internet
- Restart service nếu quá nhiều reconnect
- Monitor logs để xem chi tiết lỗi

### Performance Issues
- Giảm số lượng coins theo dõi
- Tăng interval giữa các analysis
- Optimize database indexes

## 🚨 Lưu Ý Quan Trọng

### ⚠️ Disclaimer
- Bot chỉ phân tích và gửi tín hiệu, KHÔNG tự động trade
- Luôn DYOR (Do Your Own Research) trước khi vào lệnh
- Quản lý rủi ro cẩn thận, chỉ trade với số tiền có thể mất

### 🔒 Bảo Mật
- Không chia sẻ Bot Token và API Keys
- Sử dụng VPS/Server riêng để chạy bot
- Backup định kỳ database và config

### 📞 Hỗ Trợ
- Kiểm tra logs khi có lỗi
- Monitor health check endpoint
- Telegram sẽ thông báo khi có lỗi hệ thống

## 📝 License

MIT License - Xem file LICENSE để biết chi tiết.

---

**Happy Trading! 🚀📈**

## Usage

Start the development server with hot reloading:

```bash
npm run dev
```

Start the production server:

```bash
npm start
```

The server will be running at http://localhost:3000 (or the port specified in your configuration).

## API Structure

API endpoints follow this structure:
```
/api/{version}/{route}
```

Example endpoints:
- `/api/v1.0/auth/login` - User login
- `/api/v1.0/auth/register` - User registration
- `/api/v1.0/user/profile` - Get user profile
- `/api/v1.0/user/update` - Update user information

## License

This project is licensed under the MIT License - see the package.json file for details.
