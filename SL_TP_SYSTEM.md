# 📊 ScalpWizard - <PERSON><PERSON> Thống Stop Loss & Take Profit

## 🎯 Tổng Quan

Hệ thống SL/TP của ScalpWizard sử dụng **2 phương pháp kết hợp**:
1. **Support/Resistance levels** (ưu tiên)
2. **Percentage-based** (fallback)

## ⚙️ Cấu Hình Hiện Tại

### Risk Management Settings
```json
{
  "riskManagement": {
    "stopLossPercent": 0.5,        // 0.5% từ entry
    "takeProfitPercent": [1, 2],   // 1% và 2% từ entry
    "riskRewardRatio": [1.5, 2]    // Tỷ lệ RR 1:1.5 và 1:2
  }
}
```

## 🔍 Cách Tính Toán Chi Tiết

### 1. Support/Resistance Calculation

#### Phương Pháp:
```javascript
calculateSupportResistance(candles, lookback = 20) {
  // Lấy 20 nến gần nhất
  const recentCandles = candles.slice(-20);
  
  // Support = Gi<PERSON> thấp nhất trong 20 nến
  const support = Math.min(...recentCandles.map(c => c.low));
  
  // Resistance = Giá cao nhất trong 20 nến  
  const resistance = Math.max(...recentCandles.map(c => c.high));
}
```

#### Ví Dụ:
- 20 nến gần nhất có giá thấp nhất: **43,000**
- 20 nến gần nhất có giá cao nhất: **43,500**
- **Support**: 43,000
- **Resistance**: 43,500

### 2. Stop Loss Calculation

#### Cho Lệnh BUY:
```javascript
if (type === 'BUY') {
  // Ưu tiên: Support level (nếu support < entry)
  if (support && support < entry) {
    stopLoss = support;
  } else {
    // Fallback: 0.5% từ entry
    stopLoss = entry * (1 - 0.5/100);
  }
}
```

#### Ví Dụ BUY:
- **Entry**: 43,250
- **Support**: 43,000 (< entry) ✅
- **Stop Loss**: **43,000** (sử dụng support)

#### Nếu không có support hợp lệ:
- **Entry**: 43,250  
- **Support**: 43,300 (> entry) ❌
- **Stop Loss**: 43,250 × (1 - 0.5%) = **43,033.75**

#### Cho Lệnh SELL:
```javascript
if (type === 'SELL') {
  // Ưu tiên: Resistance level (nếu resistance > entry)
  if (resistance && resistance > entry) {
    stopLoss = resistance;
  } else {
    // Fallback: 0.5% từ entry
    stopLoss = entry * (1 + 0.5/100);
  }
}
```

#### Ví Dụ SELL:
- **Entry**: 43,250
- **Resistance**: 43,500 (> entry) ✅  
- **Stop Loss**: **43,500** (sử dụng resistance)

### 3. Take Profit Calculation

#### Cho Lệnh BUY:
```javascript
// Luôn sử dụng % từ entry
takeProfit = entry * (1 + takeProfitPercent[0]/100);
```

#### Ví Dụ BUY:
- **Entry**: 43,250
- **Take Profit**: 43,250 × (1 + 1%) = **43,682.50**

#### Cho Lệnh SELL:
```javascript
// Luôn sử dụng % từ entry  
takeProfit = entry * (1 - takeProfitPercent[0]/100);
```

#### Ví Dụ SELL:
- **Entry**: 43,250
- **Take Profit**: 43,250 × (1 - 1%) = **42,817.50**

## 📊 Ví Dụ Thực Tế

### Scenario 1: BUY Signal
```
Entry: 43,250
Support (20 nến): 43,000  
Resistance (20 nến): 43,500

Stop Loss: 43,000 (support level)
Take Profit: 43,682.50 (1% từ entry)
Risk/Reward: 250/432.50 = 1:1.73
```

### Scenario 2: SELL Signal  
```
Entry: 43,250
Support (20 nến): 43,000
Resistance (20 nến): 43,500

Stop Loss: 43,500 (resistance level)  
Take Profit: 42,817.50 (1% từ entry)
Risk/Reward: 250/432.50 = 1:1.73
```

### Scenario 3: BUY với Support không hợp lệ
```
Entry: 43,250
Support: 43,300 (> entry, không dùng được)

Stop Loss: 43,033.75 (0.5% từ entry)
Take Profit: 43,682.50 (1% từ entry)  
Risk/Reward: 216.25/432.50 = 1:2
```

## 🎛️ Tùy Chỉnh Hệ Thống

### 1. Thay Đổi Stop Loss %
```json
{
  "riskManagement": {
    "stopLossPercent": 1.0  // Tăng lên 1%
  }
}
```

### 2. Thay Đổi Take Profit %
```json
{
  "riskManagement": {
    "takeProfitPercent": [1.5, 3]  // TP1: 1.5%, TP2: 3%
  }
}
```

### 3. Thay Đổi Lookback Period
Chỉnh sửa trong `lib/trading/indicators.js`:
```javascript
calculateSupportResistance(candles, lookback = 50) // Tăng lên 50 nến
```

## 🔄 Quá Trình Thực Thi

### 1. Khi Tạo Signal
```javascript
// Trong signalAnalyzer.js
const { stopLoss, takeProfit } = indicators.calculateSLTP(entry, type, candles);

const signal = {
  symbol: 'BTCUSDT',
  type: 'BUY', 
  entry: 43250,
  stopLoss: 43000,    // Từ support level
  takeProfit: 43682   // 1% từ entry
};
```

### 2. Khi Theo Dõi Lệnh
```javascript
// Trong orderManager.js
if (signal.checkStopLoss(currentPrice)) {
  await this.executeStopLoss(signal, currentPrice);
}

if (signal.checkTakeProfit(currentPrice)) {
  await this.executeTakeProfit(signal, currentPrice);
}
```

### 3. Methods Kiểm Tra
```javascript
// Trong tradingSignal model
checkStopLoss(currentPrice) {
  if (this.type === 'BUY') {
    return currentPrice <= this.stopLoss;  // Giá <= SL
  } else {
    return currentPrice >= this.stopLoss;  // Giá >= SL  
  }
}

checkTakeProfit(currentPrice) {
  if (this.type === 'BUY') {
    return currentPrice >= this.takeProfit; // Giá >= TP
  } else {
    return currentPrice <= this.takeProfit; // Giá <= TP
  }
}
```

## 📈 Ưu & Nhược Điểm

### ✅ Ưu Điểm
- **Smart SL**: Sử dụng support/resistance thực tế
- **Flexible**: Fallback về % nếu cần
- **Risk Management**: Tỷ lệ RR hợp lý
- **Automatic**: Tự động tính toán và theo dõi

### ⚠️ Nhược Điểm
- **Simple S/R**: Chỉ dùng min/max của 20 nến
- **Fixed TP**: Luôn dùng % cố định
- **No Trailing**: Không có trailing stop
- **Single TP**: Chỉ 1 TP level

## 🔧 Cải Tiến Có Thể

### 1. Advanced Support/Resistance
- Sử dụng pivot points
- Volume-weighted levels
- Multiple timeframe analysis

### 2. Dynamic Take Profit
- Multiple TP levels
- Trailing take profit
- ATR-based TP

### 3. Advanced Stop Loss
- Trailing stop loss
- ATR-based SL
- Time-based SL

### 4. Risk Reward Optimization
- Dynamic RR based on market conditions
- Volatility-adjusted SL/TP

## 📊 Monitoring SL/TP

### Logs để Theo Dõi
```bash
# Xem signals với SL/TP
grep "Signal saved" logs/system-*.log

# Xem kết quả SL/TP
grep "Stop Loss\|Take Profit" logs/system-*.log

# Thống kê win/loss
curl localhost:6886/api/v1/trading/statistics
```

### Key Metrics
- **Hit Rate**: % signals chạm TP vs SL
- **Average RR**: Tỷ lệ risk/reward trung bình
- **SL Accuracy**: % SL được đặt đúng support/resistance

## 🎯 Kết Luận

Hệ thống SL/TP hiện tại:
- ✅ **Intelligent**: Ưu tiên support/resistance
- ✅ **Safe**: Fallback về % cố định
- ✅ **Automatic**: Tự động tính toán và theo dõi
- ✅ **Configurable**: Có thể tùy chỉnh parameters

**Phù hợp cho trading tự động với risk management tốt! 📊💰**
