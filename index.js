const express = require('express');
const cors = require('cors');
require('dotenv').config();

// Global variables
global._ = require('lodash');
global.config = require('config');
global.Logger = require('./lib/logger');
global.mongoose = require('mongoose');
global.fs = require('fs');
global.moment = require('moment');
global.async = require('async');
global.ms = require('ms');
global.MailUtil = require('./lib/util/mail');
global.logger = Logger(`${__dirname}/logs`);

// Load models
fs.readdirSync(`${__dirname}/lib/models`).forEach((file) => {
  global[_.upperFirst(_.camelCase(file.replace('.js', 'Model')))] = require(`./lib/models/${file}`);
});

// Middleware
const bodyParser = require('body-parser');
const tokenToUserMiddleware = require('./lib/middleware/tokenToUser');

// Handle routes
const AuthRoutes = require('./lib/routes/auth');
const UserRoutes = require('./lib/routes/user');

// Trading modules
const marketDataService = require('./lib/services/marketDataService');
const signalService = require('./lib/services/signalService');
const statisticsService = require('./lib/services/statisticsService');
const reportScheduler = require('./lib/services/reportScheduler');
const orderManager = require('./lib/trading/orderManager');
const scheduler = require('./lib/trading/scheduler');
const telegramBot = require('./lib/trading/telegramBot');

// Start server
const app = express();
app.set('trust proxy', true);
const server = require('http').Server(app);
global.io = require('socket.io')(server);

// Middleware setup
app.use(cors());
app.use(bodyParser.json({ limit: '50mb' }));
app.use(express.static('public'));

// Define route declaration function
const declareRoute = (method, routeName, middlewares = [], destinationRoute) => {
  if (!destinationRoute || !routeName) {
    return;
  }

  Object.keys(destinationRoute).forEach((version) => {
    app[method](`/api/${version}${routeName}`, middlewares, destinationRoute[version]);
  });
};

// API Routes - Example routes for the template
declareRoute('post', '/auth/login', [], AuthRoutes.login);
declareRoute('post', '/auth/register', [], AuthRoutes.register);
declareRoute('post', '/user/profile', [tokenToUserMiddleware], UserRoutes.profile);
declareRoute('post', '/user/update', [tokenToUserMiddleware], UserRoutes.update);

// Trading API endpoints
app.get('/api/v1/trading/status', (_req, res) => {
  try {
    const status = {
      marketData: marketDataService.getStatus(),
      signal: signalService.getStatus(),
      orderManager: orderManager.getMonitoringStatus(),
      scheduler: scheduler.getStatus(),
      timestamp: new Date()
    };
    res.json(status);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/v1/trading/statistics', async (req, res) => {
  try {
    const days = parseInt(req.query.days) || 30;
    const statistics = await signalService.getSignalStatistics(days);
    res.json(statistics);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/v1/trading/signals/recent', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 10;
    const signals = await signalService.getRecentSignals(limit);
    res.json(signals);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Statistics routes
app.use('/api/statistics', require('./lib/routes/statistics'));

// Health check endpoint
app.get('/health', (_req, res) => {
  res.json({ status: 'ok', timestamp: new Date() });
});

// Initialize trading system
async function initializeTradingSystem() {
  try {
    logger.logInfo('Initializing ScalpWizard Trading System...');

    // Kết nối MongoDB
    const mongoUri = `mongodb://${config.mongo.connections.master.host}:${config.mongo.connections.master.port}/${config.mongo.connections.master.database}`;
    await mongoose.connect(mongoUri, config.mongo.connections.master.options);
    logger.logInfo('MongoDB connected successfully');

    // Khởi tạo các services theo thứ tự
    await marketDataService.initialize();
    await signalService.initialize();
    await orderManager.startMonitoring();
    await scheduler.start();

    // Khởi tạo report scheduler cho báo cáo hàng ngày
    reportScheduler.start();

    logger.logInfo('ScalpWizard Trading System initialized successfully');

  } catch (error) {
    logger.logError('Error initializing trading system:', error.message);

    // Gửi thông báo lỗi qua Telegram
    try {
      await telegramBot.sendErrorNotification(error, 'System Initialization');
    } catch (telegramError) {
      logger.logError('Error sending Telegram notification:', telegramError.message);
    }

    process.exit(1);
  }
}

// Graceful shutdown
async function gracefulShutdown() {
  try {
    logger.logInfo('Graceful shutdown initiated...');

    await scheduler.stop();
    reportScheduler.stop();
    orderManager.stopMonitoring();
    await signalService.stop();
    await marketDataService.stop();

    await mongoose.connection.close();

    logger.logInfo('Graceful shutdown completed');
    process.exit(0);
  } catch (error) {
    logger.logError('Error during graceful shutdown:', error.message);
    process.exit(1);
  }
}

const port = _.get(config, 'port', 3000);
server.listen(port, async () => {
  logger.logInfo('Server listening at port:', port);

  // Khởi tạo trading system sau khi server start
  await initializeTradingSystem();
});

// Process event handlers
process.on('uncaughtException', (err) => {
  logger.logError('uncaughtException', err);
  gracefulShutdown();
});

process.on('unhandledRejection', (reason, promise) => {
  logger.logError('Unhandled Rejection at:', promise, 'reason:', reason);
  gracefulShutdown();
});

process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);
