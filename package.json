{"name": "nodejs-backend-template", "version": "1.0.0", "description": "A clean Node.js backend template with MongoDB and Redis", "main": "index.js", "scripts": {"test": "node test-bot.js", "start": "node index.js", "dev": "nodemon index.js", "test-bot": "node test-bot.js", "pm2:start": "pm2 start index.js --name scalpwizard", "pm2:stop": "pm2 stop scalpwizard", "pm2:restart": "pm2 restart scalpwizard", "pm2:logs": "pm2 logs scalpwizard", "setup": "cp .env.example .env && echo 'Please edit .env file with your configuration'"}, "license": "MIT", "dependencies": {"async": "^3.2.4", "axios": "^1.6.0", "bcryptjs": "^2.4.3", "binance-api-node": "^0.12.4", "config": "^3.3.9", "cors": "^2.8.5", "cron": "^3.1.6", "dotenv": "^16.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.29.4", "mongoose": "^7.5.0", "ms": "^2.1.3", "node-telegram-bot-api": "^0.64.0", "nodemailer": "^6.9.5", "redis": "^4.6.8", "rr": "^0.1.0", "socket.io": "^4.7.2", "technicalindicators": "^3.1.0", "uuid": "^11.1.0", "winston": "2.4.6", "winston-daily-rotate-file": "3.10.0", "ws": "^8.14.2"}, "devDependencies": {"nodemon": "^3.0.1"}}