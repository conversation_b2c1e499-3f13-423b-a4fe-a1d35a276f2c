const express = require('express');
const router = express.Router();
const statisticsService = require('../services/statisticsService');
const reportScheduler = require('../services/reportScheduler');

/**
 * <PERSON><PERSON><PERSON> thống kê tổng quan
 */
router.get('/overall', async (req, res) => {
  try {
    const days = parseInt(req.query.days) || 30;
    const statistics = await statisticsService.getOverallStatistics(days);
    
    res.json({
      success: true,
      data: statistics,
      period: `${days} ngày`
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * L<PERSON>y thống kê theo timeframe
 */
router.get('/timeframes', async (req, res) => {
  try {
    const days = parseInt(req.query.days) || 30;
    const statistics = await statisticsService.getTimeframeStatistics(days);
    
    res.json({
      success: true,
      data: statistics,
      period: `${days} ngày`
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * <PERSON><PERSON><PERSON> thống kê theo symbol
 */
router.get('/symbols', async (req, res) => {
  try {
    const days = parseInt(req.query.days) || 30;
    const limit = parseInt(req.query.limit) || 10;
    const statistics = await statisticsService.getSymbolStatistics(days, limit);
    
    res.json({
      success: true,
      data: statistics,
      period: `${days} ngày`,
      limit
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * Tạo báo cáo đầy đủ
 */
router.get('/report', async (req, res) => {
  try {
    const report = await statisticsService.generateDailyReport();
    
    if (!report) {
      return res.status(500).json({
        success: false,
        message: 'Không thể tạo báo cáo'
      });
    }
    
    res.json({
      success: true,
      data: report
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * Gửi báo cáo thủ công qua Telegram
 */
router.post('/send-report', async (req, res) => {
  try {
    const days = parseInt(req.body.days) || 30;
    const success = await reportScheduler.sendManualReport(days);
    
    if (success) {
      res.json({
        success: true,
        message: 'Báo cáo đã được gửi qua Telegram'
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Không thể gửi báo cáo'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * Xóa cache thống kê
 */
router.post('/clear-cache', async (req, res) => {
  try {
    statisticsService.clearCache();
    
    res.json({
      success: true,
      message: 'Cache đã được xóa'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * Lấy trạng thái report scheduler
 */
router.get('/scheduler-status', async (req, res) => {
  try {
    const status = reportScheduler.getStatus();
    
    res.json({
      success: true,
      data: status
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * Cập nhật PnL cho signal
 */
router.post('/update-pnl', async (req, res) => {
  try {
    const { signalId, exitPrice } = req.body;
    
    if (!signalId || !exitPrice) {
      return res.status(400).json({
        success: false,
        message: 'Thiếu signalId hoặc exitPrice'
      });
    }
    
    const success = await statisticsService.updateSignalPnL(signalId, exitPrice);
    
    if (success) {
      res.json({
        success: true,
        message: 'PnL đã được cập nhật'
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Không thể cập nhật PnL'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * Lấy thống kê win/loss theo ngày
 */
router.get('/daily', async (req, res) => {
  try {
    const days = parseInt(req.query.days) || 7;
    const TradingSignal = require('../models/tradingSignal');
    
    const pipeline = [
      {
        $match: {
          createdAt: { 
            $gte: new Date(Date.now() - days * 24 * 60 * 60 * 1000) 
          },
          status: { $in: ['hit_tp', 'hit_sl'] },
          isConflictNotification: false
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { 
              format: "%Y-%m-%d", 
              date: "$createdAt" 
            }
          },
          signals: { $push: '$$ROOT' }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ];
    
    const results = await TradingSignal.aggregate(pipeline);
    const dailyStats = {};
    
    for (const result of results) {
      const date = result._id;
      const signals = result.signals;
      dailyStats[date] = statisticsService.calculateStatistics(signals);
    }
    
    res.json({
      success: true,
      data: dailyStats,
      period: `${days} ngày`
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

module.exports = router;
