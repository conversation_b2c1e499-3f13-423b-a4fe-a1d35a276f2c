const axios = require('axios');
const WebSocket = require('ws');

class BinanceClient {
  constructor() {
    this.baseURL = config.trading.binance.baseURL;
    this.wsBaseURL = config.trading.binance.wsBaseURL;
    this.wsConnections = new Map();
    this.candleCallbacks = new Map();
    this.reconnectAttempts = new Map();
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 5000;
  }

  /**
   * L<PERSON>y danh sách top coins theo volume 24h
   */
  async getTop24hVolumeCoins(limit = 30) {
    try {
      const response = await axios.get(`${this.baseURL}/fapi/v1/ticker/24hr`);

      // Lọc và sắp xếp theo volume
      const sortedCoins = response.data
        .filter(coin => coin.symbol.endsWith('USDT'))
        .sort((a, b) => parseFloat(b.quoteVolume) - parseFloat(a.quoteVolume))
        .slice(0, limit)
        .map(coin => coin.symbol);

      logger.logInfo(`Top ${limit} volume coins:`, sortedCoins);
      return sortedCoins;
    } catch (error) {
      logger.logError('Error getting top volume coins:', error.message);
      throw error;
    }
  }

  /**
   * Lấy dữ liệu klines (candlestick) lịch sử
   */
  async getKlines(symbol, interval, limit = 200) {
    try {
      const response = await axios.get(`${this.baseURL}/fapi/v1/klines`, {
        params: {
          symbol,
          interval,
          limit
        }
      });

      return response.data.map(kline => ({
        openTime: new Date(kline[0]),
        open: parseFloat(kline[1]),
        high: parseFloat(kline[2]),
        low: parseFloat(kline[3]),
        close: parseFloat(kline[4]),
        volume: parseFloat(kline[5]),
        closeTime: new Date(kline[6]),
        quoteVolume: parseFloat(kline[7]),
        trades: parseInt(kline[8])
      }));
    } catch (error) {
      logger.logError(`Error getting klines for ${symbol}:`, error.message);
      throw error;
    }
  }

  /**
   * Lấy giá hiện tại của symbol
   */
  async getCurrentPrice(symbol) {
    try {
      const response = await axios.get(`${this.baseURL}/fapi/v1/ticker/price`, {
        params: { symbol }
      });
      return parseFloat(response.data.price);
    } catch (error) {
      logger.logError(`Error getting current price for ${symbol}:`, error.message);
      throw error;
    }
  }

  /**
   * Đăng ký callback cho dữ liệu nến real-time
   */
  subscribeCandlestick(symbol, interval, callback) {
    const streamName = `${symbol.toLowerCase()}@kline_${interval}`;
    const key = `${symbol}_${interval}`;

    this.candleCallbacks.set(key, callback);
    this.connectWebSocket(streamName, key);
  }

  /**
   * Hủy đăng ký callback
   */
  unsubscribeCandlestick(symbol, interval) {
    const key = `${symbol}_${interval}`;

    if (this.wsConnections.has(key)) {
      this.wsConnections.get(key).close();
      this.wsConnections.delete(key);
    }

    this.candleCallbacks.delete(key);
    this.reconnectAttempts.delete(key);
  }

  /**
   * Kết nối WebSocket
   */
  connectWebSocket(streamName, key) {
    const wsUrl = `${this.wsBaseURL}${streamName}`;

    try {
      const ws = new WebSocket(wsUrl);

      ws.on('open', () => {
        logger.logInfo(`WebSocket connected: ${streamName}`);
        this.reconnectAttempts.set(key, 0);
      });

      ws.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          if (message.k && message.k.x) { // Nến đã đóng
            const kline = message.k;
            const candleData = {
              symbol: kline.s,
              openTime: new Date(kline.t),
              closeTime: new Date(kline.T),
              open: parseFloat(kline.o),
              high: parseFloat(kline.h),
              low: parseFloat(kline.l),
              close: parseFloat(kline.c),
              volume: parseFloat(kline.v),
              quoteVolume: parseFloat(kline.q),
              trades: parseInt(kline.n),
              interval: kline.i
            };

            const callback = this.candleCallbacks.get(key);
            if (callback) {
              callback(candleData);
            }
          }
        } catch (error) {
          logger.logError(`Error parsing WebSocket message for ${key}:`, error.message);
        }
      });

      ws.on('close', () => {
        logger.warn(`WebSocket closed: ${streamName}`);
        this.handleReconnect(streamName, key);
      });

      ws.on('error', (error) => {
        logger.logError(`WebSocket error for ${streamName}:`, error.message);
        this.handleReconnect(streamName, key);
      });

      this.wsConnections.set(key, ws);

    } catch (error) {
      logger.logError(`Error creating WebSocket for ${streamName}:`, error.message);
      this.handleReconnect(streamName, key);
    }
  }

  /**
   * Xử lý reconnect WebSocket
   */
  handleReconnect(streamName, key) {
    const attempts = this.reconnectAttempts.get(key) || 0;

    if (attempts < this.maxReconnectAttempts) {
      this.reconnectAttempts.set(key, attempts + 1);

      setTimeout(() => {
        logger.logInfo(`Reconnecting WebSocket (${attempts + 1}/${this.maxReconnectAttempts}): ${streamName}`);
        this.connectWebSocket(streamName, key);
      }, this.reconnectDelay * (attempts + 1));
    } else {
      logger.logError(`Max reconnect attempts reached for ${streamName}`);
      this.candleCallbacks.delete(key);
      this.reconnectAttempts.delete(key);
    }
  }

  /**
   * Đóng tất cả kết nối WebSocket
   */
  closeAllConnections() {
    for (const [, ws] of this.wsConnections) {
      ws.close();
    }
    this.wsConnections.clear();
    this.candleCallbacks.clear();
    this.reconnectAttempts.clear();
  }

  /**
   * Kiểm tra trạng thái kết nối
   */
  getConnectionStatus() {
    const status = {};
    for (const [key, ws] of this.wsConnections) {
      status[key] = ws.readyState === WebSocket.OPEN ? 'connected' : 'disconnected';
    }
    return status;
  }
}

module.exports = new BinanceClient();
