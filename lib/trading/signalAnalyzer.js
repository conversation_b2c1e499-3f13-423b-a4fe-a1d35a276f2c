const indicators = require('./indicators');
const TradingSignal = require('../models/tradingSignal');

class SignalAnalyzer {
  constructor() {
    this.timeframes = config.trading.timeframes;
  }

  /**
   * <PERSON>ân tích tín hiệu cho một symbol và timeframe
   */
  async analyzeSignal(symbol, timeframe, candles) {
    try {
      if (!candles || candles.length < 220) {
        return null;
      }

      // Tính toán các chỉ báo
      const indicatorData = indicators.calculateAllIndicators(candles);
      if (!indicatorData) {
        return null;
      }

      // Kiểm tra MACD crossover
      const macdCrossover = indicators.checkMACDCrossover(indicatorData.macd, candles);

      // Kiểm tra điều kiện BUY
      const buySignal = this.checkBuyConditions(indicatorData, macdCrossover);
      if (buySignal.isValid) {
        return this.createSignal(symbol, timeframe, 'BUY', indicatorData, candles);
      }

      // Kiểm tra điều kiện SELL
      const sellSignal = this.checkSellConditions(indicatorData, macdCrossover);
      if (sellSignal.isValid) {
        return this.createSignal(symbol, timeframe, 'SELL', indicatorData, candles);
      }

      return null;
    } catch (error) {
      logger.logError(`Error analyzing signal for ${symbol} ${timeframe}:`, error.message);
      return null;
    }
  }

  /**
   * Kiểm tra điều kiện BUY
   */
  checkBuyConditions(indicatorData, macdCrossover) {
    const conditions = {
      priceAboveEMA200: false,
      emaAlignment: false,
      macdCrossover: false,
      rsiZone: false,
      strongBodyOrEngulfing: false
    };

    // 1. Giá đóng nến > EMA200
    if (indicatorData.currentPrice > indicatorData.ema200) {
      conditions.priceAboveEMA200 = true;
    }

    // 2. EMA50 > EMA200
    if (indicatorData.ema50 > indicatorData.ema200) {
      conditions.emaAlignment = true;
    }

    // 3. MACD vừa cắt lên signal
    if (macdCrossover === 'bullish') {
      conditions.macdCrossover = true;
    }

    // 4. RSI trong vùng 50-70
    if (indicators.checkRSIZone(indicatorData.rsi, 'BUY')) {
      conditions.rsiZone = true;
    }

    // 5. Body mạnh HOẶC Engulfing pattern
    const hasStrongBullishBody = indicatorData.strongBody?.isStrong &&
                                 indicatorData.strongBody?.direction === 'bullish';
    const hasBullishEngulfing = indicatorData.engulfing === 'bullish';

    if (hasStrongBullishBody || hasBullishEngulfing) {
      conditions.strongBodyOrEngulfing = true;
    }

    // Logic cuối cùng: Core + Signal + Pattern (rất linh hoạt)
    const coreConditions = conditions.priceAboveEMA200 && conditions.emaAlignment;
    const signalCondition = conditions.macdCrossover || conditions.rsiZone;
    const patternCondition = conditions.strongBodyOrEngulfing;

    // Linh hoạt: Core + Signal là đủ, Pattern là bonus
    // Nếu có MACD crossover hoặc RSI zone + Core → Valid
    const isValid = coreConditions && signalCondition;

    return {
      isValid,
      conditions,
      reason: this.getConditionsSummary(conditions, 'BUY'),
      details: {
        coreConditions,
        signalCondition,
        patternCondition,
        strongBodyPercent: indicatorData.strongBody?.bodyPercent || 0
      }
    };
  }

  /**
   * Kiểm tra điều kiện SELL
   */
  checkSellConditions(indicatorData, macdCrossover) {
    const conditions = {
      priceBelowEMA200: false,
      emaAlignment: false,
      macdCrossover: false,
      rsiZone: false,
      strongBodyOrEngulfing: false
    };

    // 1. Giá đóng nến < EMA200
    if (indicatorData.currentPrice < indicatorData.ema200) {
      conditions.priceBelowEMA200 = true;
    }

    // 2. EMA50 < EMA200
    if (indicatorData.ema50 < indicatorData.ema200) {
      conditions.emaAlignment = true;
    }

    // 3. MACD vừa cắt xuống signal
    if (macdCrossover === 'bearish') {
      conditions.macdCrossover = true;
    }

    // 4. RSI trong vùng 30-50
    if (indicators.checkRSIZone(indicatorData.rsi, 'SELL')) {
      conditions.rsiZone = true;
    }

    // 5. Body mạnh HOẶC Engulfing pattern
    const hasStrongBearishBody = indicatorData.strongBody?.isStrong &&
                                 indicatorData.strongBody?.direction === 'bearish';
    const hasBearishEngulfing = indicatorData.engulfing === 'bearish';

    if (hasStrongBearishBody || hasBearishEngulfing) {
      conditions.strongBodyOrEngulfing = true;
    }

    // Logic cuối cùng: Core + Signal + Pattern (rất linh hoạt)
    const coreConditions = conditions.priceBelowEMA200 && conditions.emaAlignment;
    const signalCondition = conditions.macdCrossover || conditions.rsiZone;
    const patternCondition = conditions.strongBodyOrEngulfing;

    // Linh hoạt: Core + Signal là đủ, Pattern là bonus
    // Nếu có MACD crossover hoặc RSI zone + Core → Valid
    const isValid = coreConditions && signalCondition;

    return {
      isValid,
      conditions,
      reason: this.getConditionsSummary(conditions, 'SELL'),
      details: {
        coreConditions,
        signalCondition,
        patternCondition,
        strongBodyPercent: indicatorData.strongBody?.bodyPercent || 0
      }
    };
  }

  /**
   * Tạo signal object
   */
  createSignal(symbol, timeframe, type, indicatorData, candles) {
    const entry = indicatorData.currentPrice;
    const sltpResult = indicators.calculateSLTP(entry, type, candles);

    const latestCandle = candles[candles.length - 1];

    return {
      symbol,
      timeframe,
      type,
      entry,
      stopLoss: sltpResult.stopLoss,
      takeProfit: sltpResult.takeProfit,
      riskReward: sltpResult.riskReward,
      tpMethod: sltpResult.tpMethod,
      atr: sltpResult.atr,
      riskAmount: sltpResult.riskAmount,
      rewardAmount: sltpResult.rewardAmount,
      indicators: {
        ema50: indicatorData.ema50,
        ema200: indicatorData.ema200,
        macd: indicatorData.macd,
        rsi: indicatorData.rsi,
        ...(indicatorData.engulfing !== 'none' && { engulfing: indicatorData.engulfing }),
        strongBody: indicatorData.strongBody
      },
      marketData: {
        open: latestCandle.open,
        high: latestCandle.high,
        low: latestCandle.low,
        close: latestCandle.close,
        volume: latestCandle.volume
      }
    };
  }

  /**
   * Tóm tắt điều kiện
   */
  getConditionsSummary(conditions, type) {
    const summary = [];

    if (type === 'BUY') {
      if (conditions.priceAboveEMA200) summary.push('✅ Giá > EMA200');
      if (conditions.emaAlignment) summary.push('✅ EMA50 > EMA200');
      if (conditions.macdCrossover) summary.push('✅ MACD cắt lên Signal');
      if (conditions.rsiZone) summary.push('✅ RSI trong vùng mua (50-70)');
      if (conditions.strongBodyOrEngulfing) summary.push('✅ Nến body mạnh hoặc Bullish Engulfing');
    } else {
      if (conditions.priceBelowEMA200) summary.push('✅ Giá < EMA200');
      if (conditions.emaAlignment) summary.push('✅ EMA50 < EMA200');
      if (conditions.macdCrossover) summary.push('✅ MACD cắt xuống Signal');
      if (conditions.rsiZone) summary.push('✅ RSI trong vùng bán (30-50)');
      if (conditions.strongBodyOrEngulfing) summary.push('✅ Nến body mạnh hoặc Bearish Engulfing');
    }

    return summary.join('\n');
  }

  /**
   * Kiểm tra xem có signal trùng lặp không
   */
  async checkDuplicateSignal(symbol, timeframe, type) {
    try {
      // Kiểm tra trong 1 giờ gần nhất
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

      const existingSignal = await TradingSignal.findOne({
        symbol,
        timeframe,
        type,
        status: 'active',
        createdAt: { $gte: oneHourAgo },
        isConflictNotification: false
      });

      return !!existingSignal;
    } catch (error) {
      logger.logError(`Error checking duplicate signal:`, error.message);
      return false;
    }
  }

  /**
   * Kiểm tra conflict với lệnh đang active
   */
  async checkActiveSignalConflict(symbol, timeframe, newSignalType) {
    try {
      const activeSignal = await TradingSignal.getActiveSignalBySymbol(symbol, timeframe);

      if (!activeSignal) {
        return { hasConflict: false };
      }

      const isSameDirection = activeSignal.type === newSignalType;
      const isOppositeDirection = activeSignal.type !== newSignalType;

      return {
        hasConflict: true,
        activeSignal,
        conflictType: isSameDirection ? 'same_direction' : 'opposite_direction',
        message: isSameDirection
          ? `Đã có lệnh ${activeSignal.type} đang chạy cho ${symbol} ${timeframe}`
          : `Có lệnh ${activeSignal.type} đang chạy, tín hiệu mới là ${newSignalType}`
      };
    } catch (error) {
      logger.logError('Error checking active signal conflict:', error.message);
      return { hasConflict: false };
    }
  }

  /**
   * Lưu signal vào database
   */
  async saveSignal(signalData) {
    try {
      // Kiểm tra trùng lặp
      const isDuplicate = await this.checkDuplicateSignal(
        signalData.symbol,
        signalData.timeframe,
        signalData.type
      );

      if (isDuplicate) {
        logger.warn(`Duplicate signal detected for ${signalData.symbol} ${signalData.timeframe} ${signalData.type}`);
        return null;
      }

      const signal = new TradingSignal(signalData);
      await signal.save();

      logger.logInfo(`Signal saved: ${signalData.symbol} ${signalData.timeframe} ${signalData.type} at ${signalData.entry}`);
      return signal;
    } catch (error) {
      logger.logError(`Error saving signal:`, error.message);
      return null;
    }
  }

  /**
   * Lấy tất cả signals đang active
   */
  async getActiveSignals() {
    try {
      return await TradingSignal.getActiveSignals();
    } catch (error) {
      logger.logError(`Error getting active signals:`, error.message);
      return [];
    }
  }

  /**
   * Cập nhật trạng thái signal
   */
  async updateSignalStatus(signalId, status, exitPrice, exitTime) {
    try {
      const signal = await TradingSignal.findById(signalId);
      if (!signal) {
        return null;
      }

      signal.status = status;
      signal.exitPrice = exitPrice;
      signal.exitTime = exitTime || new Date();

      // Tính toán P&L
      signal.pnlPercent = signal.calculatePnL(exitPrice);

      await signal.save();

      logger.logInfo(`Signal updated: ${signal.symbol} ${signal.type} - ${status} at ${exitPrice}`);
      return signal;
    } catch (error) {
      logger.logError(`Error updating signal status:`, error.message);
      return null;
    }
  }
}

module.exports = new SignalAnalyzer();
