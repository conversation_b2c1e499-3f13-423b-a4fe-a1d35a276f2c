const indicators = require('./indicators');
const TradingSignal = require('../models/tradingSignal');

class SignalAnalyzer {
  constructor() {
    this.timeframes = config.trading.timeframes;
  }

  /**
   * <PERSON>ân tích tín hiệu cho một symbol và timeframe
   */
  async analyzeSignal(symbol, timeframe, candles) {
    try {
      if (!candles || candles.length < 220) {
        return null;
      }

      // Tính toán các chỉ báo
      const indicatorData = indicators.calculateAllIndicators(candles);
      if (!indicatorData) {
        return null;
      }

      // Kiểm tra MACD crossover
      const macdCrossover = indicators.checkMACDCrossover(indicatorData.macd, candles);

      // Kiểm tra điều kiện BUY
      const buySignal = this.checkBuyConditions(indicatorData, macdCrossover);
      if (buySignal.isValid) {
        return this.createSignal(symbol, timeframe, 'BUY', indicatorData, candles);
      }

      // Kiểm tra điều kiện SELL
      const sellSignal = this.checkSellConditions(indicatorData, macdCrossover);
      if (sellSignal.isValid) {
        return this.createSignal(symbol, timeframe, 'SELL', indicatorData, candles);
      }

      return null;
    } catch (error) {
      logger.logError(`Error analyzing signal for ${symbol} ${timeframe}:`, error.message);
      return null;
    }
  }

  /**
   * Kiểm tra điều kiện BUY
   */
  checkBuyConditions(indicatorData, macdCrossover) {
    const conditions = {
      priceAboveEMA200: false,
      emaAlignment: false,
      macdCrossover: false,
      rsiZone: false,
      engulfing: false
    };

    // 1. Giá đóng nến > EMA200
    if (indicatorData.currentPrice > indicatorData.ema200) {
      conditions.priceAboveEMA200 = true;
    }

    // 2. EMA50 > EMA200
    if (indicatorData.ema50 > indicatorData.ema200) {
      conditions.emaAlignment = true;
    }

    // 3. MACD vừa cắt lên signal
    if (macdCrossover === 'bullish') {
      conditions.macdCrossover = true;
    }

    // 4. RSI > 50 và trong vùng 55-65
    if (indicators.checkRSIZone(indicatorData.rsi, 'BUY')) {
      conditions.rsiZone = true;
    }

    // 5. Nến cuối là bullish engulfing
    if (indicatorData.engulfing === 'bullish') {
      conditions.engulfing = true;
    }

    const isValid = Object.values(conditions).every(condition => condition === true);

    return {
      isValid,
      conditions,
      reason: this.getConditionsSummary(conditions, 'BUY')
    };
  }

  /**
   * Kiểm tra điều kiện SELL
   */
  checkSellConditions(indicatorData, macdCrossover) {
    const conditions = {
      priceBelowEMA200: false,
      emaAlignment: false,
      macdCrossover: false,
      rsiZone: false,
      engulfing: false
    };

    // 1. Giá đóng nến < EMA200
    if (indicatorData.currentPrice < indicatorData.ema200) {
      conditions.priceBelowEMA200 = true;
    }

    // 2. EMA50 < EMA200
    if (indicatorData.ema50 < indicatorData.ema200) {
      conditions.emaAlignment = true;
    }

    // 3. MACD vừa cắt xuống signal
    if (macdCrossover === 'bearish') {
      conditions.macdCrossover = true;
    }

    // 4. RSI < 50 và trong vùng 35-45
    if (indicators.checkRSIZone(indicatorData.rsi, 'SELL')) {
      conditions.rsiZone = true;
    }

    // 5. Nến cuối là bearish engulfing
    if (indicatorData.engulfing === 'bearish') {
      conditions.engulfing = true;
    }

    const isValid = Object.values(conditions).every(condition => condition === true);

    return {
      isValid,
      conditions,
      reason: this.getConditionsSummary(conditions, 'SELL')
    };
  }

  /**
   * Tạo signal object
   */
  createSignal(symbol, timeframe, type, indicatorData, candles) {
    const entry = indicatorData.currentPrice;
    const sltpResult = indicators.calculateSLTP(entry, type, candles);

    const latestCandle = candles[candles.length - 1];

    return {
      symbol,
      timeframe,
      type,
      entry,
      stopLoss: sltpResult.stopLoss,
      takeProfit: sltpResult.takeProfit,
      riskReward: sltpResult.riskReward,
      tpMethod: sltpResult.tpMethod,
      atr: sltpResult.atr,
      riskAmount: sltpResult.riskAmount,
      rewardAmount: sltpResult.rewardAmount,
      indicators: {
        ema50: indicatorData.ema50,
        ema200: indicatorData.ema200,
        macd: indicatorData.macd,
        rsi: indicatorData.rsi,
        engulfing: indicatorData.engulfing
      },
      marketData: {
        open: latestCandle.open,
        high: latestCandle.high,
        low: latestCandle.low,
        close: latestCandle.close,
        volume: latestCandle.volume
      }
    };
  }

  /**
   * Tóm tắt điều kiện
   */
  getConditionsSummary(conditions, type) {
    const summary = [];

    if (type === 'BUY') {
      if (conditions.priceAboveEMA200) summary.push('✅ Giá > EMA200');
      if (conditions.emaAlignment) summary.push('✅ EMA50 > EMA200');
      if (conditions.macdCrossover) summary.push('✅ MACD cắt lên Signal');
      if (conditions.rsiZone) summary.push('✅ RSI trong vùng mua');
      if (conditions.engulfing) summary.push('✅ Bullish Engulfing');
    } else {
      if (conditions.priceBelowEMA200) summary.push('✅ Giá < EMA200');
      if (conditions.emaAlignment) summary.push('✅ EMA50 < EMA200');
      if (conditions.macdCrossover) summary.push('✅ MACD cắt xuống Signal');
      if (conditions.rsiZone) summary.push('✅ RSI trong vùng bán');
      if (conditions.engulfing) summary.push('✅ Bearish Engulfing');
    }

    return summary.join('\n');
  }

  /**
   * Kiểm tra xem có signal trùng lặp không
   */
  async checkDuplicateSignal(symbol, timeframe, type) {
    try {
      // Kiểm tra trong 1 giờ gần nhất
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

      const existingSignal = await TradingSignal.findOne({
        symbol,
        timeframe,
        type,
        status: 'active',
        createdAt: { $gte: oneHourAgo }
      });

      return !!existingSignal;
    } catch (error) {
      logger.logError(`Error checking duplicate signal:`, error.message);
      return false;
    }
  }

  /**
   * Lưu signal vào database
   */
  async saveSignal(signalData) {
    try {
      // Kiểm tra trùng lặp
      const isDuplicate = await this.checkDuplicateSignal(
        signalData.symbol,
        signalData.timeframe,
        signalData.type
      );

      if (isDuplicate) {
        logger.warn(`Duplicate signal detected for ${signalData.symbol} ${signalData.timeframe} ${signalData.type}`);
        return null;
      }

      const signal = new TradingSignal(signalData);
      await signal.save();

      logger.logInfo(`Signal saved: ${signalData.symbol} ${signalData.timeframe} ${signalData.type} at ${signalData.entry}`);
      return signal;
    } catch (error) {
      logger.logError(`Error saving signal:`, error.message);
      return null;
    }
  }

  /**
   * Lấy tất cả signals đang active
   */
  async getActiveSignals() {
    try {
      return await TradingSignal.getActiveSignals();
    } catch (error) {
      logger.logError(`Error getting active signals:`, error.message);
      return [];
    }
  }

  /**
   * Cập nhật trạng thái signal
   */
  async updateSignalStatus(signalId, status, exitPrice, exitTime) {
    try {
      const signal = await TradingSignal.findById(signalId);
      if (!signal) {
        return null;
      }

      signal.status = status;
      signal.exitPrice = exitPrice;
      signal.exitTime = exitTime || new Date();

      // Tính toán P&L
      signal.pnlPercent = signal.calculatePnL(exitPrice);

      await signal.save();

      logger.logInfo(`Signal updated: ${signal.symbol} ${signal.type} - ${status} at ${exitPrice}`);
      return signal;
    } catch (error) {
      logger.logError(`Error updating signal status:`, error.message);
      return null;
    }
  }
}

module.exports = new SignalAnalyzer();
