const TelegramBot = require('node-telegram-bot-api');

class TelegramNotifier {
  constructor() {
    this.config = config.telegram;
    this.bot = null;
    this.isEnabled = this.config.enabled;

    if (this.isEnabled && this.config.botToken) {
      this.initBot();
    }
  }

  /**
   * Khởi tạo Telegram Bot
   */
  initBot() {
    try {
      this.bot = new TelegramBot(this.config.botToken, { polling: false });
      logger.logInfo('Telegram bot initialized successfully');
    } catch (error) {
      logger.logError('Error initializing Telegram bot:', error.message);
      this.isEnabled = false;
    }
  }

  /**
   * <PERSON><PERSON><PERSON> thông báo tín hiệu trading
   */
  async sendSignalNotification(signal) {
    if (!this.isEnabled || !this.bot) {
      return null;
    }

    try {
      const message = this.formatSignalMessage(signal);

      const sentMessage = await this.bot.sendMessage(this.config.chatId, message, {
        parse_mode: 'HTML',
        disable_web_page_preview: true
      });

      logger.logInfo(`Signal notification sent for ${signal.symbol} ${signal.type}`);
      return sentMessage.message_id;
    } catch (error) {
      logger.logError('Error sending signal notification:', error.message);
      return null;
    }
  }

  /**
   * Gửi thông báo kết quả lệnh (với reply về signal gốc)
   */
  async sendResultNotification(signal, statistics, resultType = null) {
    if (!this.isEnabled || !this.bot) {
      return null;
    }

    try {
      const message = this.formatResultMessage(signal, statistics, resultType);

      const options = {
        parse_mode: 'HTML',
        disable_web_page_preview: true
      };

      // Reply về tin nhắn signal gốc nếu có
      if (signal.telegramMessageId) {
        options.reply_to_message_id = parseInt(signal.telegramMessageId);
      }

      const sentMessage = await this.bot.sendMessage(this.config.chatId, message, options);

      logger.logInfo(`Result notification sent for ${signal.symbol} ${signal.type} - ${signal.status}`);
      return sentMessage.message_id;
    } catch (error) {
      logger.logError('Error sending result notification:', error.message);
      return false;
    }
  }

  /**
   * Gửi thông báo conflict signal
   */
  async sendConflictNotification(newSignal, activeSignal, conflictType) {
    if (!this.isEnabled || !this.bot) {
      return null;
    }

    try {
      const message = this.formatConflictMessage(newSignal, activeSignal, conflictType);

      const options = {
        parse_mode: 'HTML',
        disable_web_page_preview: true
      };

      // Reply về tin nhắn signal đang active
      if (activeSignal.telegramMessageId) {
        options.reply_to_message_id = parseInt(activeSignal.telegramMessageId);
      }

      const sentMessage = await this.bot.sendMessage(this.config.chatId, message, options);

      logger.logInfo(`Conflict notification sent for ${newSignal.symbol} ${newSignal.type}`);
      return sentMessage.message_id;
    } catch (error) {
      logger.logError('Error sending conflict notification:', error.message);
      return null;
    }
  }

  /**
   * Gửi update notification (trailing stop, etc.) với reply
   */
  async sendUpdateNotification(signal, updateType, updateData) {
    if (!this.isEnabled || !this.bot) {
      return null;
    }

    try {
      const message = this.formatUpdateMessage(signal, updateType, updateData);

      const options = {
        parse_mode: 'HTML',
        disable_web_page_preview: true
      };

      // Reply về tin nhắn signal gốc
      if (signal.telegramMessageId) {
        options.reply_to_message_id = parseInt(signal.telegramMessageId);
      }

      const sentMessage = await this.bot.sendMessage(this.config.chatId, message, options);

      logger.logInfo(`Update notification sent for ${signal.symbol} - ${updateType}`);
      return sentMessage.message_id;
    } catch (error) {
      logger.logError('Error sending update notification:', error.message);
      return null;
    }
  }

  /**
   * Gửi thông báo thống kê định kỳ
   */
  async sendStatisticsNotification(statistics) {
    if (!this.isEnabled || !this.bot) {
      return null;
    }

    try {
      const message = this.formatStatisticsMessage(statistics);

      await this.bot.sendMessage(this.config.chatId, message, {
        parse_mode: 'HTML',
        disable_web_page_preview: true
      });

      logger.logInfo('Statistics notification sent');
      return true;
    } catch (error) {
      logger.logError('Error sending statistics notification:', error.message);
      return false;
    }
  }

  /**
   * Format thông báo tín hiệu
   */
  formatSignalMessage(signal) {
    const template = this.config.messageFormat.signal;
    const indicators = this.formatIndicators(signal.indicators, signal);

    // Tính toán các giá trị hiển thị
    const riskPercent = ((signal.riskAmount || 0) / signal.entry * 100).toFixed(2);
    const rewardPercent = ((signal.rewardAmount || 0) / signal.entry * 100).toFixed(2);
    const tpMethodDisplay = this.formatTPMethod(signal.tpMethod || 'percentage');

    return template
      .replace('{symbol}', signal.symbol)
      .replace('{time}', moment().format('DD/MM/YYYY HH:mm:ss'))
      .replace('{timeframe}', signal.timeframe)
      .replace('{type}', signal.type === 'BUY' ? '📈 MUA' : '📉 BÁN')
      .replace('{entry}', signal.entry.toFixed(6))
      .replace('{sl}', signal.stopLoss.toFixed(6))
      .replace('{tp}', signal.takeProfit.toFixed(6))
      .replace('{rr}', (signal.riskReward || 0).toFixed(1))
      .replace('{tpMethod}', tpMethodDisplay)
      .replace('{riskAmount}', (signal.riskAmount || 0).toFixed(6))
      .replace('{riskPercent}', riskPercent)
      .replace('{rewardAmount}', (signal.rewardAmount || 0).toFixed(6))
      .replace('{rewardPercent}', rewardPercent)
      .replace('{indicators}', indicators)
      .replace('{symbol}', signal.symbol)
      .replace('{timeframe}', signal.timeframe);
  }

  /**
   * Format thông báo kết quả
   */
  formatResultMessage(signal, statistics, resultType = null) {
    const template = this.config.messageFormat.result;

    // Win/Loss dựa trên PnL thực tế, không phải status
    const isWin = signal.pnlPercent > 0;
    const result = isWin ? '✅ WIN' : '❌ LOSS';
    const resultIcon = isWin ? '🎯' : '🛑';
    const finalResultType = resultType || (signal.status === 'hit_tp' ? 'Take Profit' : 'Stop Loss');

    return template
      .replace('{symbol}', signal.symbol)
      .replace('{closeTime}', moment(signal.exitTime).format('DD/MM/YYYY HH:mm:ss'))
      .replace('{timeframe}', signal.timeframe)
      .replace('{type}', signal.type === 'BUY' ? '📈 MUA' : '📉 BÁN')
      .replace('{entry}', signal.entry.toFixed(6))
      .replace('{exit}', signal.exitPrice.toFixed(6))
      .replace('{result}', `${resultIcon} ${result}`)
      .replace('{resultType}', finalResultType)
      .replace('{pnl}', signal.pnlPercent > 0 ? `+${signal.pnlPercent.toFixed(2)}` : signal.pnlPercent.toFixed(2))
      .replace('{totalWin}', statistics.winTrades || 0)
      .replace('{totalLoss}', statistics.lossTrades || 0)
      .replace('{winRate}', (statistics.winRate || 0).toFixed(1))
      .replace('{symbol}', signal.symbol)
      .replace('{timeframe}', signal.timeframe);
  }

  /**
   * Format thông báo thống kê
   */
  formatStatisticsMessage(statistics) {
    const message = `📊 **THỐNG KÊ TRADING** 📊

⏰ **Thời gian:** ${moment().format('DD/MM/YYYY HH:mm:ss')}

📈 **Tổng quan 30 ngày:**
📊 **Tổng lệnh:** ${statistics.totalTrades || 0}
✅ **Win:** ${statistics.winTrades || 0}
❌ **Loss:** ${statistics.lossTrades || 0}
📊 **Win Rate:** ${(statistics.winRate || 0).toFixed(1)}%

💰 **P&L:**
📈 **Tổng P&L:** ${statistics.totalPnL > 0 ? '+' : ''}${(statistics.totalPnL || 0).toFixed(2)}%
📊 **P&L Trung bình:** ${statistics.avgPnL > 0 ? '+' : ''}${(statistics.avgPnL || 0).toFixed(2)}%

#ScalpWizard #Statistics`;

    return message;
  }

  /**
   * Format thông tin chỉ báo
   */
  formatIndicators(indicators, signal = null) {
    const lines = [];

    lines.push(`📊 <b>EMA50:</b> ${indicators.ema50.toFixed(6)}`);
    lines.push(`📊 <b>EMA200:</b> ${indicators.ema200.toFixed(6)}`);

    if (indicators.macd) {
      lines.push(`📊 <b>MACD:</b> ${indicators.macd.macd.toFixed(6)}`);
      lines.push(`📊 <b>Signal:</b> ${indicators.macd.signal.toFixed(6)}`);
    }

    lines.push(`📊 <b>RSI:</b> ${indicators.rsi.toFixed(2)}`);

    // Hiển thị pattern
    const patternText = this.getPatternText(indicators);
    lines.push(`🕯️ <b>Pattern:</b> ${patternText}`);

    // Hiển thị điều kiện đã thỏa mãn nếu có signal
    if (signal && signal.conditions) {
      lines.push(`\n✅ <b>Điều kiện thỏa mãn:</b>`);
      lines.push(signal.conditions);
    }

    return lines.join('\n');
  }

  /**
   * Lấy text cho pattern
   */
  getPatternText(indicators) {
    const patterns = [];

    // Engulfing pattern
    if (indicators.engulfing) {
      patterns.push(indicators.engulfing === 'bullish' ? '🟢 Bullish Engulfing' : '🔴 Bearish Engulfing');
    }

    // Strong body
    if (indicators.strongBody && indicators.strongBody.isStrong) {
      const direction = indicators.strongBody.direction === 'bullish' ? '🟢' : '🔴';
      patterns.push(`${direction} Body mạnh (${indicators.strongBody.bodyPercent.toFixed(1)}%)`);
    }

    return patterns.length > 0 ? patterns.join(', ') : '⚪ Không có pattern đặc biệt';
  }

  /**
   * Lấy emoji cho pattern
   */
  getPatternEmoji(pattern) {
    switch (pattern) {
      case 'bullish':
        return '🟢 Bullish Engulfing';
      case 'bearish':
        return '🔴 Bearish Engulfing';
      default:
        return '⚪ None';
    }
  }

  /**
   * Format TP Method cho hiển thị
   */
  formatTPMethod(method) {
    if (method.includes('resistance')) {
      const strength = method.split('_')[1] || '';
      return `🏔️ Resistance ${strength}`;
    } else if (method.includes('support')) {
      const strength = method.split('_')[1] || '';
      return `🏔️ Support ${strength}`;
    } else if (method.includes('atr')) {
      return '📊 ATR Based';
    } else if (method.includes('dynamic_rr')) {
      const rr = method.split('_')[2] || '';
      return `⚡ Dynamic RR ${rr}`;
    } else {
      return '📈 Percentage';
    }
  }

  /**
   * Format thông báo conflict
   */
  formatConflictMessage(newSignal, activeSignal, conflictType) {
    const conflictIcon = conflictType === 'same_direction' ? '⚠️' : '🔄';
    const conflictTitle = conflictType === 'same_direction' ? 'TRÙNG LỆNH' : 'LỆNH NGƯỢC CHIỀU';

    const activeTime = moment(activeSignal.createdAt).format('DD/MM HH:mm');
    const newTime = moment().format('DD/MM HH:mm');

    return `${conflictIcon} <b>${conflictTitle}</b> ${conflictIcon}

📊 <b>Cặp:</b> ${newSignal.symbol} ${newSignal.timeframe}

🔴 <b>Lệnh đang chạy:</b>
${activeSignal.type === 'BUY' ? '📈' : '📉'} <b>${activeSignal.type}</b> tại <b>${activeSignal.entry}</b>
⏰ <b>Thời gian:</b> ${activeTime}
🛑 <b>SL:</b> ${activeSignal.stopLoss}
🎯 <b>TP:</b> ${activeSignal.takeProfit}

🆕 <b>Tín hiệu mới:</b>
${newSignal.type === 'BUY' ? '📈' : '📉'} <b>${newSignal.type}</b> tại <b>${newSignal.entry}</b>
⏰ <b>Thời gian:</b> ${newTime}
🛑 <b>SL:</b> ${newSignal.stopLoss}
🎯 <b>TP:</b> ${newSignal.takeProfit}

💡 <b>Khuyến nghị:</b> ${this.getConflictRecommendation(conflictType)}

#${newSignal.symbol}_${newSignal.timeframe} #Conflict`;
  }

  /**
   * Format thông báo update
   */
  formatUpdateMessage(signal, updateType, updateData) {
    const updateIcons = {
      'trailing_stop': '🔄',
      'partial_tp': '🎯',
      'sl_moved': '🛑',
      'tp_extended': '🎯'
    };

    const updateTitles = {
      'trailing_stop': 'TRAILING STOP',
      'partial_tp': 'CHỐT LỜI MỘT PHẦN',
      'sl_moved': 'DI CHUYỂN STOP LOSS',
      'tp_extended': 'MỞ RỘNG TAKE PROFIT'
    };

    const icon = updateIcons[updateType] || '📊';
    const title = updateTitles[updateType] || 'CẬP NHẬT LỆNH';

    return `${icon} <b>${title}</b> ${icon}

📊 <b>Cặp:</b> ${signal.symbol} ${signal.timeframe}
${signal.type === 'BUY' ? '📈' : '📉'} <b>Loại:</b> ${signal.type}
💰 <b>Entry:</b> ${signal.entry}

${this.formatUpdateDetails(updateType, updateData)}

⏰ <b>Thời gian:</b> ${moment().format('DD/MM/YYYY HH:mm:ss')}

#${signal.symbol}_${signal.timeframe} #Update`;
  }

  /**
   * Lấy khuyến nghị cho conflict
   */
  getConflictRecommendation(conflictType) {
    if (conflictType === 'same_direction') {
      return 'Giữ lệnh hiện tại, bỏ qua tín hiệu mới';
    } else {
      return 'Cân nhắc đóng lệnh cũ nếu tín hiệu mới mạnh hơn';
    }
  }

  /**
   * Format chi tiết update
   */
  formatUpdateDetails(updateType, updateData) {
    switch (updateType) {
      case 'trailing_stop':
        return `🔄 <b>SL mới:</b> ${updateData.newStopLoss}
💰 <b>Giá hiện tại:</b> ${updateData.currentPrice}`;

      case 'partial_tp':
        return `🎯 <b>Đã chốt:</b> ${updateData.percentage}% tại ${updateData.price}
💰 <b>Lời còn lại:</b> ${updateData.remainingAmount}`;

      case 'sl_moved':
        return `🛑 <b>SL cũ:</b> ${updateData.oldStopLoss}
🛑 <b>SL mới:</b> ${updateData.newStopLoss}`;

      case 'tp_extended':
        return `🎯 <b>TP cũ:</b> ${updateData.oldTakeProfit}
🎯 <b>TP mới:</b> ${updateData.newTakeProfit}`;

      default:
        return `📊 <b>Thông tin:</b> ${JSON.stringify(updateData)}`;
    }
  }

  /**
   * Gửi thông báo lỗi
   */
  async sendErrorNotification(error, context = '') {
    if (!this.isEnabled || !this.bot) {
      return null;
    }

    try {
      const message = `🚨 **LỖI HỆ THỐNG** 🚨

⏰ **Thời gian:** ${moment().format('DD/MM/YYYY HH:mm:ss')}
📍 **Context:** ${context}
❌ **Lỗi:** ${error.message}

#ScalpWizard #Error`;

      await this.bot.sendMessage(this.config.chatId, message, {
        parse_mode: 'HTML'
      });

      return true;
    } catch (err) {
      logger.logError('Error sending error notification:', err.message);
      return false;
    }
  }

  /**
   * Gửi thông báo trạng thái hệ thống
   */
  async sendStatusNotification(status, message) {
    if (!this.isEnabled || !this.bot) {
      return null;
    }

    try {
      const statusIcon = status === 'online' ? '🟢' : '🔴';
      const notification = `${statusIcon} <b>TRẠNG THÁI HỆ THỐNG</b>

⏰ <b>Thời gian:</b> ${moment().format('DD/MM/YYYY HH:mm:ss')}
📊 <b>Trạng thái:</b> ${status.toUpperCase()}
📝 <b>Thông tin:</b> ${message}

#ScalpWizard #Status`;

      await this.bot.sendMessage(this.config.chatId, notification, {
        parse_mode: 'HTML'
      });

      return true;
    } catch (error) {
      logger.logError('Error sending status notification:', error.message);
      return false;
    }
  }

  /**
   * Kiểm tra kết nối bot
   */
  async testConnection() {
    if (!this.isEnabled || !this.bot) {
      return false;
    }

    try {
      await this.bot.getMe();
      return true;
    } catch (error) {
      logger.logError('Telegram bot connection test failed:', error.message);
      return false;
    }
  }
}

module.exports = new TelegramNotifier();
