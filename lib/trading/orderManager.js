const binanceClient = require('./binanceClient');
const signalAnalyzer = require('./signalAnalyzer');
const telegramBot = require('./telegramBot');
const trailingManager = require('./trailingManager');
const TradingSignal = require('../models/tradingSignal');

class OrderManager {
  constructor() {
    this.activeSignals = new Map();
    this.priceMonitorInterval = 5000; // 5 giây
    this.monitoringActive = false;
  }

  /**
   * Bắt đầu theo dõi các lệnh active
   */
  async startMonitoring() {
    if (this.monitoringActive) {
      return;
    }

    this.monitoringActive = true;
    logger.logInfo('Order monitoring started');

    // Load active signals từ database
    await this.loadActiveSignals();

    // Bắt đầu monitoring loop
    this.monitoringLoop();
  }

  /**
   * Dừng theo dõi
   */
  stopMonitoring() {
    this.monitoringActive = false;
    logger.logInfo('Order monitoring stopped');
  }

  /**
   * Load active signals từ database
   */
  async loadActiveSignals() {
    try {
      const signals = await signalAnalyzer.getActiveSignals();

      for (const signal of signals) {
        this.activeSignals.set(signal._id.toString(), signal);
      }

      logger.logInfo(`Loaded ${signals.length} active signals for monitoring`);
    } catch (error) {
      logger.logError('Error loading active signals:', error.message);
    }
  }

  /**
   * Thêm signal mới vào monitoring
   */
  addSignalToMonitoring(signal) {
    this.activeSignals.set(signal._id.toString(), signal);

    // Thêm vào trailing manager nếu enabled
    if (config.trading.riskManagement.dynamicTP.enabled) {
      trailingManager.addSignalToTrailing(signal);
    }

    logger.logInfo(`Added signal to monitoring: ${signal.symbol} ${signal.type}`);
  }

  /**
   * Xóa signal khỏi monitoring
   */
  removeSignalFromMonitoring(signalId) {
    this.activeSignals.delete(signalId);
    logger.logInfo(`Removed signal from monitoring: ${signalId}`);
  }

  /**
   * Loop chính để monitoring
   */
  async monitoringLoop() {
    while (this.monitoringActive) {
      try {
        await this.checkAllSignals();
        await this.sleep(this.priceMonitorInterval);
      } catch (error) {
        logger.logError('Error in monitoring loop:', error.message);
        await this.sleep(this.priceMonitorInterval);
      }
    }
  }

  /**
   * Kiểm tra tất cả signals
   */
  async checkAllSignals() {
    if (this.activeSignals.size === 0) {
      return;
    }

    const signalPromises = Array.from(this.activeSignals.values()).map(signal =>
      this.checkSignal(signal)
    );

    await Promise.allSettled(signalPromises);
  }

  /**
   * Kiểm tra một signal cụ thể
   */
  async checkSignal(signal) {
    try {
      // Lấy giá hiện tại
      const currentPrice = await binanceClient.getCurrentPrice(signal.symbol);

      if (!currentPrice) {
        return;
      }

      const signalId = signal._id.toString();

      // Cập nhật trailing nếu enabled
      if (config.trading.riskManagement.dynamicTP.enabled) {
        await trailingManager.updateTrailing(signalId, currentPrice);

        // Kiểm tra trailing SL/TP
        const trailingCheck = trailingManager.checkTrailingSLTP(signalId, currentPrice);

        if (trailingCheck.hitSL) {
          await this.executeStopLoss(signal, currentPrice, true); // trailing = true
          return;
        }

        if (trailingCheck.hitTP) {
          await this.executeTakeProfit(signal, currentPrice, true); // trailing = true
          return;
        }
      } else {
        // Kiểm tra SL/TP thông thường
        if (signal.checkStopLoss(currentPrice)) {
          await this.executeStopLoss(signal, currentPrice);
          return;
        }

        if (signal.checkTakeProfit(currentPrice)) {
          await this.executeTakeProfit(signal, currentPrice);
          return;
        }
      }

    } catch (error) {
      logger.logError(`Error checking signal ${signal.symbol}:`, error.message);
    }
  }

  /**
   * Thực hiện Stop Loss
   */
  async executeStopLoss(signal, currentPrice, isTrailing = false) {
    try {
      logger.logInfo(`Stop Loss hit for ${signal.symbol} ${signal.type} at ${currentPrice}`);

      // Cập nhật signal trong database
      const updatedSignal = await signalAnalyzer.updateSignalStatus(
        signal._id,
        'hit_sl',
        currentPrice,
        new Date()
      );

      if (updatedSignal) {
        // Xóa khỏi monitoring và trailing
        this.removeSignalFromMonitoring(signal._id.toString());
        trailingManager.removeSignalFromTrailing(signal._id.toString());

        // Lấy thống kê
        const statistics = await this.getStatistics();

        // Gửi thông báo Telegram với thông tin trailing
        const resultType = isTrailing ? 'Trailing SL' : 'Stop Loss';
        await telegramBot.sendResultNotification(updatedSignal, statistics, resultType);

        logger.logInfo(`${resultType} executed for ${signal.symbol}: ${signal.type} - Loss: ${updatedSignal.pnlPercent.toFixed(2)}%`);
      }
    } catch (error) {
      logger.logError(`Error executing stop loss for ${signal.symbol}:`, error.message);
    }
  }

  /**
   * Thực hiện Take Profit
   */
  async executeTakeProfit(signal, currentPrice, isTrailing = false) {
    try {
      logger.logInfo(`Take Profit hit for ${signal.symbol} ${signal.type} at ${currentPrice}`);

      // Cập nhật signal trong database
      const updatedSignal = await signalAnalyzer.updateSignalStatus(
        signal._id,
        'hit_tp',
        currentPrice,
        new Date()
      );

      if (updatedSignal) {
        // Xóa khỏi monitoring và trailing
        this.removeSignalFromMonitoring(signal._id.toString());
        trailingManager.removeSignalFromTrailing(signal._id.toString());

        // Lấy thống kê
        const statistics = await this.getStatistics();

        // Gửi thông báo Telegram với thông tin trailing
        const resultType = isTrailing ? 'Trailing TP' : 'Take Profit';
        await telegramBot.sendResultNotification(updatedSignal, statistics, resultType);

        logger.logInfo(`${resultType} executed for ${signal.symbol}: ${signal.type} - Profit: ${updatedSignal.pnlPercent.toFixed(2)}%`);
      }
    } catch (error) {
      logger.logError(`Error executing take profit for ${signal.symbol}:`, error.message);
    }
  }

  /**
   * Lấy thống kê trading
   */
  async getStatistics() {
    try {
      const stats = await TradingSignal.getStatistics(30);
      return stats.length > 0 ? stats[0] : {
        totalTrades: 0,
        winTrades: 0,
        lossTrades: 0,
        winRate: 0,
        totalPnL: 0,
        avgPnL: 0
      };
    } catch (error) {
      logger.logError('Error getting statistics:', error.message);
      return {
        totalTrades: 0,
        winTrades: 0,
        lossTrades: 0,
        winRate: 0,
        totalPnL: 0,
        avgPnL: 0
      };
    }
  }

  /**
   * Lấy trạng thái monitoring
   */
  getMonitoringStatus() {
    return {
      isActive: this.monitoringActive,
      activeSignalsCount: this.activeSignals.size,
      activeSignals: Array.from(this.activeSignals.values()).map(signal => ({
        id: signal._id,
        symbol: signal.symbol,
        type: signal.type,
        entry: signal.entry,
        stopLoss: signal.stopLoss,
        takeProfit: signal.takeProfit,
        createdAt: signal.createdAt
      }))
    };
  }

  /**
   * Hủy signal thủ công
   */
  async cancelSignal(signalId, reason = 'Manual cancellation') {
    try {
      const signal = await TradingSignal.findById(signalId);
      if (!signal || signal.status !== 'active') {
        return false;
      }

      // Cập nhật trạng thái
      signal.status = 'cancelled';
      signal.exitTime = new Date();
      await signal.save();

      // Xóa khỏi monitoring
      this.removeSignalFromMonitoring(signalId);

      logger.logInfo(`Signal cancelled: ${signal.symbol} ${signal.type} - ${reason}`);
      return true;
    } catch (error) {
      logger.logError(`Error cancelling signal ${signalId}:`, error.message);
      return false;
    }
  }

  /**
   * Hủy tất cả signals của một symbol
   */
  async cancelSignalsBySymbol(symbol, reason = 'Symbol delisted') {
    try {
      const signals = await TradingSignal.find({
        symbol,
        status: 'active'
      });

      for (const signal of signals) {
        await this.cancelSignal(signal._id.toString(), reason);
      }

      logger.logInfo(`Cancelled ${signals.length} signals for ${symbol}`);
      return signals.length;
    } catch (error) {
      logger.logError(`Error cancelling signals for ${symbol}:`, error.message);
      return 0;
    }
  }

  /**
   * Cleanup old signals
   */
  async cleanupOldSignals(days = 30) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);

      const result = await TradingSignal.deleteMany({
        createdAt: { $lt: cutoffDate },
        status: { $in: ['hit_tp', 'hit_sl', 'cancelled'] }
      });

      logger.logInfo(`Cleaned up ${result.deletedCount} old signals`);
      return result.deletedCount;
    } catch (error) {
      logger.logError('Error cleaning up old signals:', error.message);
      return 0;
    }
  }

  /**
   * Sleep utility
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = new OrderManager();
