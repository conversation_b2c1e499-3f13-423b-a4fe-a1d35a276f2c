const { EMA, MACD, RSI } = require('technicalindicators');
const config = require('config');
const Logger = require('../logger');
const logger = Logger(`${__dirname}/../../logs`);

class TechnicalIndicators {
  constructor() {
    this.config = config.trading.indicators;
  }

  /**
   * Tính toán EMA (Exponential Moving Average) với SMA seed như Binance/TradingView
   */
  calculateEMA(prices, period) {
    if (prices.length < period) {
      return null;
    }

    // Sử dụng SMA seed method như Binance/TradingView thay vì first value seed
    return this.calculateEMAWithSMASeed(prices, period);
  }

  /**
   * Tính EMA với SMA seed (chuẩn Binance/TradingView)
   */
  calculateEMAWithSMASeed(prices, period) {
    if (prices.length < period) {
      return null;
    }

    // Tính SMA cho 'period' giá trị đầu làm seed
    const smaSum = prices.slice(0, period).reduce((sum, price) => sum + price, 0);
    const sma = smaSum / period;

    const multiplier = 2 / (period + 1);
    let ema = sma;

    // Tính EMA từ giá trị thứ (period+1) trở đi
    for (let i = period; i < prices.length; i++) {
      ema = (prices[i] * multiplier) + (ema * (1 - multiplier));
    }

    return ema;
  }

  /**
   * Tính toán MACD
   */
  calculateMACD(prices) {
    if (prices.length < this.config.macd.slow + this.config.macd.signal) {
      return null;
    }

    const macdInput = {
      values: prices,
      fastPeriod: this.config.macd.fast,
      slowPeriod: this.config.macd.slow,
      signalPeriod: this.config.macd.signal,
      SimpleMAOscillator: false,
      SimpleMASignal: false
    };

    const macdResult = MACD.calculate(macdInput);
    if (macdResult.length === 0) return null;

    const latest = macdResult[macdResult.length - 1];
    return {
      macd: latest.MACD,
      signal: latest.signal,
      histogram: latest.histogram
    };
  }

  /**
   * Tính toán RSI (Relative Strength Index)
   */
  calculateRSI(prices) {
    if (prices.length < this.config.rsi.period + 1) {
      return null;
    }

    const rsiInput = {
      values: prices,
      period: this.config.rsi.period
    };

    const rsiResult = RSI.calculate(rsiInput);
    return rsiResult[rsiResult.length - 1]; // Lấy giá trị cuối cùng
  }

  /**
   * Kiểm tra mô hình nến Engulfing
   */
  checkEngulfingPattern(candles) {
    if (candles.length < 2) {
      return 'none';
    }

    const prev = candles[candles.length - 2];
    const current = candles[candles.length - 1];

    // Bullish Engulfing
    if (
      prev.close < prev.open && // Nến trước là nến đỏ
      current.close > current.open && // Nến hiện tại là nến xanh
      current.open < prev.close && // Mở cửa nến hiện tại thấp hơn đóng cửa nến trước
      current.close > prev.open // Đóng cửa nến hiện tại cao hơn mở cửa nến trước
    ) {
      return 'bullish';
    }

    // Bearish Engulfing
    if (
      prev.close > prev.open && // Nến trước là nến xanh
      current.close < current.open && // Nến hiện tại là nến đỏ
      current.open > prev.close && // Mở cửa nến hiện tại cao hơn đóng cửa nến trước
      current.close < prev.open // Đóng cửa nến hiện tại thấp hơn mở cửa nến trước
    ) {
      return 'bearish';
    }

    return 'none';
  }

  /**
   * Kiểm tra nến có body mạnh (body chiếm >70% tổng chiều cao nến)
   */
  checkStrongBodyCandle(candle, minBodyPercent = 70) {
    const totalHeight = candle.high - candle.low;
    if (totalHeight === 0) return false;

    const bodyHeight = Math.abs(candle.close - candle.open);
    const bodyPercent = (bodyHeight / totalHeight) * 100;

    return {
      isStrong: bodyPercent >= minBodyPercent,
      bodyPercent: bodyPercent,
      direction: candle.close > candle.open ? 'bullish' : 'bearish'
    };
  }

  /**
   * Tính toán tất cả chỉ báo cho một dataset
   */
  calculateAllIndicators(candles) {
    // Yêu cầu tối thiểu: ít nhất 220 nến để tính EMA200
    // Tối ưu: 300+ nến cho độ chính xác cao nhất
    if (!candles || candles.length < 220) {
      return null;
    }

    // Log warning nếu không đủ 300 nến để người dùng biết
    if (candles.length < 300) {
      logger.warn(`⚠️ Using ${candles.length} candles (optimal: 300+). EMA200 accuracy may be reduced.`);
    }

    // ⚠️ VALIDATION: Đảm bảo dữ liệu được sắp xếp theo thời gian tăng dần
    const isCorrectOrder = candles.length > 1 &&
      candles[0].openTime <= candles[candles.length - 1].openTime;

    if (!isCorrectOrder) {
      logger.warn('⚠️ WARNING: Candles data is not sorted in ascending order by time!');
      // Sắp xếp lại nếu cần
      candles.sort((a, b) => new Date(a.openTime) - new Date(b.openTime));
    }

    const closes = candles.map(c => c.close);
    const highs = candles.map(c => c.high);
    const lows = candles.map(c => c.low);

    const ema50 = this.calculateEMA(closes, this.config.ema.fast);
    const ema200 = this.calculateEMA(closes, this.config.ema.slow);
    const macd = this.calculateMACD(closes);
    const rsi = this.calculateRSI(closes);
    const engulfing = this.checkEngulfingPattern(candles);

    // Kiểm tra nến có body mạnh (giảm từ 70% xuống 60%)
    const latestCandle = candles[candles.length - 1];
    const strongBody = this.checkStrongBodyCandle(latestCandle, 60);

    const currentPrice = closes[closes.length - 1];

    return {
      ema50,
      ema200,
      macd,
      rsi,
      engulfing,
      strongBody,
      currentPrice: currentPrice,
      previousPrice: closes[closes.length - 2]
    };
  }

  /**
   * Kiểm tra điều kiện MACD crossover
   */
  checkMACDCrossover(currentMACD, previousCandles) {
    if (!currentMACD || previousCandles.length < 2) {
      return 'none';
    }

    // Tính MACD của nến trước
    const prevCloses = previousCandles.slice(0, -1).map(c => c.close);
    const prevMACD = this.calculateMACD(prevCloses);

    if (!prevMACD) {
      return 'none';
    }

    // Kiểm tra crossover
    const currentDiff = currentMACD.macd - currentMACD.signal;
    const prevDiff = prevMACD.macd - prevMACD.signal;

    if (prevDiff <= 0 && currentDiff > 0) {
      return 'bullish'; // MACD cắt lên signal
    }

    if (prevDiff >= 0 && currentDiff < 0) {
      return 'bearish'; // MACD cắt xuống signal
    }

    return 'none';
  }

  /**
   * Kiểm tra RSI trong vùng mua/bán
   */
  checkRSIZone(rsi, type) {
    if (!rsi) return false;

    if (type === 'BUY') {
      return rsi > 50 && rsi >= this.config.rsi.buyZone[0] && rsi <= this.config.rsi.buyZone[1];
    } else if (type === 'SELL') {
      return rsi < 50 && rsi >= this.config.rsi.sellZone[0] && rsi <= this.config.rsi.sellZone[1];
    }

    return false;
  }

  /**
   * Tính toán Support và Resistance levels
   */
  calculateSupportResistance(candles, lookback = 20) {
    if (candles.length < lookback) {
      return { support: null, resistance: null };
    }

    const recentCandles = candles.slice(-lookback);
    const highs = recentCandles.map(c => c.high);
    const lows = recentCandles.map(c => c.low);

    // Tìm support (đáy gần nhất)
    const support = Math.min(...lows);

    // Tìm resistance (đỉnh gần nhất)
    const resistance = Math.max(...highs);

    return { support, resistance };
  }

  /**
   * Tính toán ATR (Average True Range)
   */
  calculateATR(candles, period = 14) {
    if (candles.length < period + 1) {
      return null;
    }

    const trueRanges = [];

    for (let i = 1; i < candles.length; i++) {
      const current = candles[i];
      const previous = candles[i - 1];

      const tr1 = current.high - current.low;
      const tr2 = Math.abs(current.high - previous.close);
      const tr3 = Math.abs(current.low - previous.close);

      trueRanges.push(Math.max(tr1, tr2, tr3));
    }

    // Tính ATR bằng EMA của True Range
    const atr = this.calculateEMA(trueRanges.slice(-period), period);
    return atr;
  }

  /**
   * Tìm vùng Support/Resistance nâng cao
   */
  findAdvancedSupportResistance(candles, lookback = 50) {
    if (candles.length < lookback) {
      return { supports: [], resistances: [] };
    }

    const recentCandles = candles.slice(-lookback);
    const supports = [];
    const resistances = [];

    // Tìm các pivot points
    for (let i = 2; i < recentCandles.length - 2; i++) {
      const current = recentCandles[i];
      const prev2 = recentCandles[i - 2];
      const prev1 = recentCandles[i - 1];
      const next1 = recentCandles[i + 1];
      const next2 = recentCandles[i + 2];

      // Pivot Low (Support)
      if (current.low < prev2.low && current.low < prev1.low &&
        current.low < next1.low && current.low < next2.low) {
        supports.push({
          price: current.low,
          strength: this.calculatePivotStrength(recentCandles, i, 'low'),
          index: i
        });
      }

      // Pivot High (Resistance)
      if (current.high > prev2.high && current.high > prev1.high &&
        current.high > next1.high && current.high > next2.high) {
        resistances.push({
          price: current.high,
          strength: this.calculatePivotStrength(recentCandles, i, 'high'),
          index: i
        });
      }
    }

    // Sắp xếp theo độ mạnh
    supports.sort((a, b) => b.strength - a.strength);
    resistances.sort((a, b) => b.strength - a.strength);

    return { supports, resistances };
  }

  /**
   * Tính độ mạnh của pivot point
   */
  calculatePivotStrength(candles, pivotIndex, type) {
    let strength = 1;
    const pivot = candles[pivotIndex];

    // Kiểm tra số lần test level này
    for (let i = 0; i < candles.length; i++) {
      if (i === pivotIndex) continue;

      const candle = candles[i];
      const tolerance = pivot[type] * 0.002; // 0.2% tolerance

      if (type === 'low') {
        if (Math.abs(candle.low - pivot.low) <= tolerance) {
          strength++;
        }
      } else {
        if (Math.abs(candle.high - pivot.high) <= tolerance) {
          strength++;
        }
      }
    }

    return strength;
  }

  /**
   * Tính toán Stop Loss và Take Profit nâng cao
   */
  calculateSLTP(entry, type, candles) {
    const riskConfig = config.trading.riskManagement;
    const atr = this.calculateATR(candles);
    const { supports, resistances } = this.findAdvancedSupportResistance(candles);
    const { support, resistance } = this.calculateSupportResistance(candles); // Fallback

    let stopLoss, takeProfit, tpMethod = 'percentage';
    const minRR = riskConfig.minRiskReward || 1.2;

    if (type === 'BUY') {
      // === STOP LOSS cho BUY ===
      stopLoss = this.calculateBuyStopLoss(entry, support, atr, riskConfig);

      // === TAKE PROFIT cho BUY ===
      const tpResult = this.calculateBuyTakeProfit(entry, stopLoss, resistances, atr, riskConfig, minRR);
      takeProfit = tpResult.price;
      tpMethod = tpResult.method;

    } else {
      // === STOP LOSS cho SELL ===
      stopLoss = this.calculateSellStopLoss(entry, resistance, atr, riskConfig);

      // === TAKE PROFIT cho SELL ===
      const tpResult = this.calculateSellTakeProfit(entry, stopLoss, supports, atr, riskConfig, minRR);
      takeProfit = tpResult.price;
      tpMethod = tpResult.method;
    }

    const riskAmount = Math.abs(entry - stopLoss);
    const rewardAmount = Math.abs(takeProfit - entry);
    const actualRR = rewardAmount / riskAmount;

    return {
      stopLoss: parseFloat(stopLoss.toFixed(8)),
      takeProfit: parseFloat(takeProfit.toFixed(8)),
      riskReward: parseFloat(actualRR.toFixed(2)),
      tpMethod,
      atr: atr ? parseFloat(atr.toFixed(8)) : null,
      riskAmount: parseFloat(riskAmount.toFixed(8)),
      rewardAmount: parseFloat(rewardAmount.toFixed(8))
    };
  }

  /**
   * Tính Stop Loss cho BUY
   */
  calculateBuyStopLoss(entry, support, atr, riskConfig) {
    // Ưu tiên 1: Support level mạnh
    if (support && support < entry) {
      const distance = entry - support;
      const entryPercent = distance / entry * 100;

      // Chỉ dùng support nếu không quá xa (< 2%)
      if (entryPercent <= 2) {
        return support;
      }
    }

    // Ưu tiên 2: ATR-based
    if (atr) {
      return entry - (atr * riskConfig.atrMultiplier.stopLoss);
    }

    // Fallback: Percentage
    return entry * (1 - riskConfig.stopLossPercent / 100);
  }

  /**
   * Tính Take Profit cho BUY
   */
  calculateBuyTakeProfit(entry, stopLoss, resistances, atr, riskConfig, minRR) {
    const riskAmount = entry - stopLoss;
    const minReward = riskAmount * minRR;
    const minTP = entry + minReward;

    // Ưu tiên 1: Resistance level gần nhất > minTP
    for (const resistance of resistances) {
      if (resistance.price > minTP && resistance.price < entry * 1.05) { // Không quá 5%
        return {
          price: resistance.price,
          method: `resistance_${resistance.strength}x`
        };
      }
    }

    // Ưu tiên 2: ATR-based nếu đủ RR
    if (atr) {
      const atrTP = entry + (atr * riskConfig.atrMultiplier.takeProfit);
      if (atrTP >= minTP) {
        return {
          price: atrTP,
          method: 'atr_based'
        };
      }
    }

    // Ưu tiên 3: Dynamic RR based
    const dynamicRR = this.calculateDynamicRR(riskAmount, entry);
    const dynamicTP = entry + (riskAmount * dynamicRR);

    return {
      price: dynamicTP,
      method: `dynamic_rr_${dynamicRR}`
    };
  }

  /**
   * Tính Stop Loss cho SELL
   */
  calculateSellStopLoss(entry, resistance, atr, riskConfig) {
    // Ưu tiên 1: Resistance level mạnh
    if (resistance && resistance > entry) {
      const distance = resistance - entry;
      const entryPercent = distance / entry * 100;

      // Chỉ dùng resistance nếu không quá xa (< 2%)
      if (entryPercent <= 2) {
        return resistance;
      }
    }

    // Ưu tiên 2: ATR-based
    if (atr) {
      return entry + (atr * riskConfig.atrMultiplier.stopLoss);
    }

    // Fallback: Percentage
    return entry * (1 + riskConfig.stopLossPercent / 100);
  }

  /**
   * Tính Take Profit cho SELL
   */
  calculateSellTakeProfit(entry, stopLoss, supports, atr, riskConfig, minRR) {
    const riskAmount = stopLoss - entry;
    const minReward = riskAmount * minRR;
    const minTP = entry - minReward;

    // Ưu tiên 1: Support level gần nhất < minTP
    for (const support of supports) {
      if (support.price < minTP && support.price > entry * 0.95) { // Không quá 5%
        return {
          price: support.price,
          method: `support_${support.strength}x`
        };
      }
    }

    // Ưu tiên 2: ATR-based nếu đủ RR
    if (atr) {
      const atrTP = entry - (atr * riskConfig.atrMultiplier.takeProfit);
      if (atrTP <= minTP) {
        return {
          price: atrTP,
          method: 'atr_based'
        };
      }
    }

    // Ưu tiên 3: Dynamic RR based
    const dynamicRR = this.calculateDynamicRR(riskAmount, entry);
    const dynamicTP = entry - (riskAmount * dynamicRR);

    return {
      price: dynamicTP,
      method: `dynamic_rr_${dynamicRR}`
    };
  }

  /**
   * Tính toán Dynamic Risk Reward dựa trên market conditions
   */
  calculateDynamicRR(riskAmount, entry) {
    const riskPercent = (riskAmount / entry) * 100;

    // RR cao hơn khi risk thấp, RR thấp hơn khi risk cao
    if (riskPercent < 0.3) {
      return 2.5; // Risk thấp, target cao
    } else if (riskPercent < 0.5) {
      return 2.0;
    } else if (riskPercent < 1.0) {
      return 1.5;
    } else {
      return 1.2; // Risk cao, target thấp
    }
  }
}

module.exports = new TechnicalIndicators();
