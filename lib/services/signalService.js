const signalAnalyzer = require('../trading/signalAnalyzer');
const telegramBot = require('../trading/telegramBot');
const orderManager = require('../trading/orderManager');
const marketDataService = require('./marketDataService');

class SignalService {
  constructor() {
    this.isRunning = false;
    this.analysisQueue = [];
    this.processingQueue = false;
    this.maxQueueSize = 100;
  }

  /**
   * Khởi tạo service
   */
  async initialize() {
    try {
      logger.logInfo('Initializing Signal Service...');

      this.isRunning = true;

      // Bắt đầu xử lý queue
      this.startQueueProcessor();

      logger.logInfo('Signal Service initialized successfully');
    } catch (error) {
      logger.logError('Error initializing Signal Service:', error.message);
      throw error;
    }
  }

  /**
   * Phân tích tín hiệu cho một symbol và timeframe
   */
  async analyzeSymbol(symbol, timeframe) {
    try {
      // Thêm vào queue để xử lý
      this.addToQueue({ symbol, timeframe, timestamp: Date.now() });
    } catch (error) {
      logger.logError(`Error adding ${symbol} ${timeframe} to analysis queue:`, error.message);
    }
  }

  /**
   * Thêm vào queue phân tích
   */
  addToQueue(analysisTask) {
    if (this.analysisQueue.length >= this.maxQueueSize) {
      // Xóa task cũ nhất nếu queue đầy
      this.analysisQueue.shift();
      logger.warn('Analysis queue is full, removing oldest task');
    }

    this.analysisQueue.push(analysisTask);
  }

  /**
   * Bắt đầu xử lý queue
   */
  startQueueProcessor() {
    if (this.processingQueue) {
      return;
    }

    this.processingQueue = true;
    this.processQueue();
  }

  /**
   * Xử lý queue phân tích
   */
  async processQueue() {
    while (this.isRunning && this.processingQueue) {
      try {
        if (this.analysisQueue.length === 0) {
          await this.sleep(1000); // Đợi 1 giây nếu queue trống
          continue;
        }

        const task = this.analysisQueue.shift();
        await this.processAnalysisTask(task);

        // Đợi một chút giữa các task để tránh overload
        await this.sleep(100);

      } catch (error) {
        logger.logError('Error in queue processor:', error.message);
        await this.sleep(1000);
      }
    }
  }

  /**
   * Xử lý một task phân tích
   */
  async processAnalysisTask(task) {
    try {
      const { symbol, timeframe } = task;

      // Lấy dữ liệu nến - tăng lên 1000 để đảm bảo có đủ 300+ nến cho EMA200 chính xác
      const candles = await marketDataService.getLatestCandles(symbol, timeframe, 1000);

      if (candles.length < 300) {
        logger.warn(`Not enough candles for ${symbol} ${timeframe}: ${candles.length} (need 300 for accurate EMA200)`);
        return;
      }

      // Phân tích tín hiệu
      const signal = await signalAnalyzer.analyzeSignal(symbol, timeframe, candles);

      if (signal) {
        await this.handleNewSignal(signal);
      }

    } catch (error) {
      logger.logError(`Error processing analysis task for ${task.symbol} ${task.timeframe}:`, error.message);
    }
  }

  /**
   * Xử lý tín hiệu mới
   */
  async handleNewSignal(signalData) {
    try {
      logger.logInfo(`New signal detected: ${signalData.symbol} ${signalData.timeframe} ${signalData.type}`);

      // Lưu signal vào database
      const savedSignal = await signalAnalyzer.saveSignal(signalData);

      if (!savedSignal) {
        logger.warn(`Signal not saved (possibly duplicate): ${signalData.symbol} ${signalData.type}`);
        return;
      }

      // Gửi thông báo Telegram
      const messageId = await telegramBot.sendSignalNotification(savedSignal);

      if (messageId) {
        // Cập nhật message ID
        savedSignal.telegramMessageId = messageId.toString();
        await savedSignal.save();
      }

      // Thêm vào order manager để theo dõi
      orderManager.addSignalToMonitoring(savedSignal);

      logger.logInfo(`Signal processed successfully: ${signalData.symbol} ${signalData.type}`);

    } catch (error) {
      logger.logError(`Error handling new signal:`, error.message);
    }
  }

  /**
   * Phân tích tất cả symbols đang track
   */
  async analyzeAllSymbols() {
    try {
      const status = marketDataService.getStatus();

      if (!status.isInitialized) {
        logger.warn('Market Data Service not initialized, skipping analysis');
        return;
      }

      for (const symbol of status.trackedSymbols) {
        for (const timeframe of status.timeframes) {
          await this.analyzeSymbol(symbol, timeframe);
        }
      }

      logger.logInfo(`Queued analysis for ${status.trackedSymbols.length} symbols, ${status.timeframes.length} timeframes`);

    } catch (error) {
      logger.logError('Error analyzing all symbols:', error.message);
    }
  }

  /**
   * Lấy thống kê signals
   */
  async getSignalStatistics(days = 30) {
    try {
      const TradingSignal = require('../models/tradingSignal');
      const stats = await TradingSignal.getStatistics(days);
      return stats.length > 0 ? stats[0] : {
        totalTrades: 0,
        winTrades: 0,
        lossTrades: 0,
        winRate: 0,
        totalPnL: 0,
        avgPnL: 0
      };
    } catch (error) {
      logger.logError('Error getting signal statistics:', error.message);
      return null;
    }
  }

  /**
   * Gửi báo cáo thống kê
   */
  async sendStatisticsReport() {
    try {
      const statistics = await this.getSignalStatistics(30);

      if (statistics && statistics.totalTrades > 0) {
        await telegramBot.sendStatisticsNotification(statistics);
        logger.logInfo('Statistics report sent');
      }

    } catch (error) {
      logger.logError('Error sending statistics report:', error.message);
    }
  }

  /**
   * Lấy signals gần đây
   */
  async getRecentSignals(limit = 10) {
    try {
      const TradingSignal = require('../models/tradingSignal');
      return await TradingSignal.find()
        .sort({ createdAt: -1 })
        .limit(limit)
        .select('symbol timeframe type entry stopLoss takeProfit status createdAt exitTime pnlPercent');
    } catch (error) {
      logger.logError('Error getting recent signals:', error.message);
      return [];
    }
  }

  /**
   * Lấy signals theo symbol
   */
  async getSignalsBySymbol(symbol, limit = 20) {
    try {
      const TradingSignal = require('../models/tradingSignal');
      return await TradingSignal.find({ symbol })
        .sort({ createdAt: -1 })
        .limit(limit);
    } catch (error) {
      logger.logError(`Error getting signals for ${symbol}:`, error.message);
      return [];
    }
  }

  /**
   * Lấy performance theo symbol
   */
  async getSymbolPerformance(days = 30) {
    try {
      const TradingSignal = require('../models/tradingSignal');
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const performance = await TradingSignal.aggregate([
        {
          $match: {
            createdAt: { $gte: startDate },
            status: { $in: ['hit_tp', 'hit_sl'] }
          }
        },
        {
          $group: {
            _id: '$symbol',
            totalTrades: { $sum: 1 },
            winTrades: {
              $sum: { $cond: [{ $eq: ['$status', 'hit_tp'] }, 1, 0] }
            },
            totalPnL: { $sum: '$pnlPercent' },
            avgPnL: { $avg: '$pnlPercent' }
          }
        },
        {
          $project: {
            symbol: '$_id',
            totalTrades: 1,
            winTrades: 1,
            winRate: {
              $multiply: [
                { $divide: ['$winTrades', '$totalTrades'] },
                100
              ]
            },
            totalPnL: { $round: ['$totalPnL', 2] },
            avgPnL: { $round: ['$avgPnL', 2] }
          }
        },
        { $sort: { totalPnL: -1 } }
      ]);

      return performance;
    } catch (error) {
      logger.logError('Error getting symbol performance:', error.message);
      return [];
    }
  }

  /**
   * Lấy trạng thái service
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      processingQueue: this.processingQueue,
      queueSize: this.analysisQueue.length,
      maxQueueSize: this.maxQueueSize
    };
  }

  /**
   * Dừng service
   */
  async stop() {
    try {
      this.isRunning = false;
      this.processingQueue = false;
      this.analysisQueue = [];

      logger.logInfo('Signal Service stopped');
    } catch (error) {
      logger.logError('Error stopping Signal Service:', error.message);
    }
  }

  /**
   * Sleep utility
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = new SignalService();
