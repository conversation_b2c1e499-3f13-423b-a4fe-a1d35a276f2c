const binanceClient = require('../trading/binanceClient');
const MarketData = require('../models/marketData');
const indicators = require('../trading/indicators');

class MarketDataService {
  constructor() {
    this.trackedSymbols = new Set();
    this.timeframes = config.trading.timeframes;
    this.candleBuffer = new Map(); // Buffer để lưu nến tạm thời
    this.isInitialized = false;
  }

  /**
   * Khởi tạo service
   */
  async initialize() {
    try {
      logger.logInfo('Initializing Market Data Service...');

      // Lấy danh sách top coins
      await this.updateTopCoins();

      // Khởi tạo WebSocket connections
      await this.initializeWebSocketConnections();

      this.isInitialized = true;
      logger.logInfo('Market Data Service initialized successfully');
    } catch (error) {
      logger.logError('Error initializing Market Data Service:', error.message);
      throw error;
    }
  }

  /**
   * Cập nhật danh sách top coins
   */
  async updateTopCoins() {
    try {
      const topCoins = await binanceClient.getTop24hVolumeCoins(config.trading.binance.maxCoinsToTrack);

      // Cập nhật tracked symbols
      const newSymbols = new Set(topCoins);
      const oldSymbols = new Set(this.trackedSymbols);

      // Symbols cần thêm
      const symbolsToAdd = [...newSymbols].filter(symbol => !oldSymbols.has(symbol));

      // Symbols cần xóa
      const symbolsToRemove = [...oldSymbols].filter(symbol => !newSymbols.has(symbol));

      // Cập nhật tracked symbols
      this.trackedSymbols = newSymbols;

      logger.logInfo(`Updated top coins: +${symbolsToAdd.length} -${symbolsToRemove.length}`);

      return {
        added: symbolsToAdd,
        removed: symbolsToRemove,
        current: [...newSymbols]
      };
    } catch (error) {
      logger.logError('Error updating top coins:', error.message);
      throw error;
    }
  }

  /**
   * Khởi tạo WebSocket connections
   */
  async initializeWebSocketConnections() {
    try {
      for (const symbol of this.trackedSymbols) {
        for (const timeframe of this.timeframes) {
          await this.subscribeToSymbol(symbol, timeframe);
        }
      }

      logger.logInfo(`Initialized WebSocket for ${this.trackedSymbols.size} symbols, ${this.timeframes.length} timeframes`);
    } catch (error) {
      logger.logError('Error initializing WebSocket connections:', error.message);
      throw error;
    }
  }

  /**
   * Đăng ký WebSocket cho một symbol và timeframe
   */
  async subscribeToSymbol(symbol, timeframe) {
    try {
      // Load historical data trước
      await this.loadHistoricalData(symbol, timeframe);

      // Đăng ký WebSocket
      binanceClient.subscribeCandlestick(symbol, timeframe, (candleData) => {
        this.handleNewCandle(candleData);
      });

      logger.logInfo(`Subscribed to ${symbol} ${timeframe}`);
    } catch (error) {
      logger.logError(`Error subscribing to ${symbol} ${timeframe}:`, error.message);
    }
  }

  /**
   * Load dữ liệu lịch sử
   */
  async loadHistoricalData(symbol, timeframe) {
    try {
      // Kiểm tra xem đã có dữ liệu chưa
      const existingData = await MarketData.findOne({
        symbol,
        timeframe
      }).sort({ openTime: -1 });

      let limit = 200;

      // Nếu đã có dữ liệu, chỉ lấy dữ liệu mới
      if (existingData) {
        const timeDiff = Date.now() - existingData.openTime.getTime();
        const intervalMs = this.getIntervalMs(timeframe);
        limit = Math.min(Math.ceil(timeDiff / intervalMs) + 10, 200);
      }

      const klines = await binanceClient.getKlines(symbol, timeframe, limit);

      // Lưu vào database
      for (const kline of klines) {
        await this.saveMarketData(symbol, timeframe, kline);
      }

      logger.logInfo(`Loaded ${klines.length} historical candles for ${symbol} ${timeframe}`);
    } catch (error) {
      logger.logError(`Error loading historical data for ${symbol} ${timeframe}:`, error.message);
    }
  }

  /**
   * Xử lý nến mới từ WebSocket
   */
  async handleNewCandle(candleData) {
    try {
      const { symbol, interval: timeframe } = candleData;

      // Lưu vào database
      await this.saveMarketData(symbol, timeframe, candleData);

      // Trigger signal analysis
      await this.triggerSignalAnalysis(symbol, timeframe);

    } catch (error) {
      logger.logError(`Error handling new candle for ${candleData.symbol}:`, error.message);
    }
  }

  /**
   * Lưu market data vào database
   */
  async saveMarketData(symbol, timeframe, candleData) {
    try {
      // Kiểm tra xem đã tồn tại chưa
      const existing = await MarketData.findOne({
        symbol,
        timeframe,
        openTime: candleData.openTime
      });

      if (existing) {
        // Cập nhật nếu đã tồn tại
        Object.assign(existing, candleData);
        await existing.save();
      } else {
        // Tạo mới
        const marketData = new MarketData({
          symbol,
          timeframe,
          ...candleData
        });
        await marketData.save();
      }
    } catch (error) {
      logger.logError(`Error saving market data for ${symbol}:`, error.message);
    }
  }

  /**
   * Trigger phân tích tín hiệu
   */
  async triggerSignalAnalysis(symbol, timeframe) {
    try {
      // Import ở đây để tránh circular dependency
      const signalService = require('./signalService');
      await signalService.analyzeSymbol(symbol, timeframe);
    } catch (error) {
      logger.logError(`Error triggering signal analysis for ${symbol}:`, error.message);
    }
  }

  /**
   * Lấy dữ liệu nến gần nhất
   */
  async getLatestCandles(symbol, timeframe, limit = 200) {
    try {
      const candles = await MarketData.getLatestCandles(symbol, timeframe, limit);

      // ⚠️ QUAN TRỌNG: Database trả về data theo thứ tự giảm dần (mới nhất trước)
      // Cần đảo ngược để có thứ tự tăng dần (cũ nhất trước) cho việc tính toán indicators
      const sortedCandles = candles.reverse();

      return sortedCandles.map(candle => ({
        openTime: candle.openTime,
        closeTime: candle.closeTime,
        open: candle.open,
        high: candle.high,
        low: candle.low,
        close: candle.close,
        volume: candle.volume,
        quoteVolume: candle.quoteVolume,
        trades: candle.trades
      }));
    } catch (error) {
      logger.logError(`Error getting latest candles for ${symbol}:`, error.message);
      return [];
    }
  }

  /**
   * Cập nhật chỉ báo cho dữ liệu mới
   */
  async updateIndicators(symbol, timeframe) {
    try {
      const candles = await this.getLatestCandles(symbol, timeframe, 200);
      if (candles.length < 50) {
        return;
      }

      const indicatorData = indicators.calculateAllIndicators(candles);
      if (!indicatorData) {
        return;
      }

      // Cập nhật chỉ báo cho nến cuối cùng
      const latestCandle = await MarketData.findOne({
        symbol,
        timeframe
      }).sort({ openTime: -1 });

      if (latestCandle) {
        latestCandle.indicators = {
          ema50: indicatorData.ema50,
          ema200: indicatorData.ema200,
          macd: indicatorData.macd,
          rsi: indicatorData.rsi
        };

        latestCandle.patterns = {
          engulfing: indicatorData.engulfing
        };

        await latestCandle.save();
      }
    } catch (error) {
      logger.logError(`Error updating indicators for ${symbol}:`, error.message);
    }
  }

  /**
   * Lấy interval milliseconds
   */
  getIntervalMs(timeframe) {
    const intervals = {
      '1m': 60 * 1000,
      '5m': 5 * 60 * 1000,
      '15m': 15 * 60 * 1000,
      '1h': 60 * 60 * 1000,
      '4h': 4 * 60 * 60 * 1000,
      '1d': 24 * 60 * 60 * 1000
    };
    return intervals[timeframe] || 60 * 1000;
  }

  /**
   * Lấy trạng thái service
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      trackedSymbolsCount: this.trackedSymbols.size,
      trackedSymbols: [...this.trackedSymbols],
      timeframes: this.timeframes,
      connectionStatus: binanceClient.getConnectionStatus()
    };
  }

  /**
   * Dừng service
   */
  async stop() {
    try {
      binanceClient.closeAllConnections();
      this.trackedSymbols.clear();
      this.candleBuffer.clear();
      this.isInitialized = false;

      logger.logInfo('Market Data Service stopped');
    } catch (error) {
      logger.logError('Error stopping Market Data Service:', error.message);
    }
  }
}

module.exports = new MarketDataService();
