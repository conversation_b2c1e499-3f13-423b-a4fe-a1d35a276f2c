const TradingSignal = require('../models/tradingSignal');
const Logger = require('../logger');
const logger = Logger(`${__dirname}/../../logs`);

class StatisticsService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 phút
  }

  /**
   * Tính win/loss dựa trên PnL thực tế
   */
  calculateWinLoss(signal) {
    // Win nếu PnL > 0, Loss nếu PnL <= 0
    if (signal.pnlPercent !== undefined && signal.pnlPercent !== null) {
      return signal.pnlPercent > 0 ? 'WIN' : 'LOSS';
    }
    
    // Fallback về logic cũ nếu không có PnL
    return signal.status === 'hit_tp' ? 'WIN' : 'LOSS';
  }

  /**
   * <PERSON><PERSON>y thống kê tổng quan
   */
  async getOverallStatistics(days = 30) {
    const cacheKey = `overall_${days}`;
    
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.data;
      }
    }

    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const signals = await TradingSignal.find({
        createdAt: { $gte: startDate },
        status: { $in: ['hit_tp', 'hit_sl'] },
        isConflictNotification: false
      }).sort({ createdAt: -1 });

      const stats = this.calculateStatistics(signals);
      
      // Cache kết quả
      this.cache.set(cacheKey, {
        data: stats,
        timestamp: Date.now()
      });

      return stats;
    } catch (error) {
      logger.logError('Error getting overall statistics:', error.message);
      return this.getEmptyStats();
    }
  }

  /**
   * Lấy thống kê theo timeframe
   */
  async getTimeframeStatistics(days = 30) {
    const cacheKey = `timeframe_${days}`;
    
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.data;
      }
    }

    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const pipeline = [
        {
          $match: {
            createdAt: { $gte: startDate },
            status: { $in: ['hit_tp', 'hit_sl'] },
            isConflictNotification: false
          }
        },
        {
          $group: {
            _id: '$timeframe',
            signals: { $push: '$$ROOT' }
          }
        }
      ];

      const results = await TradingSignal.aggregate(pipeline);
      const timeframeStats = {};

      for (const result of results) {
        const timeframe = result._id;
        const signals = result.signals;
        timeframeStats[timeframe] = this.calculateStatistics(signals);
      }

      // Cache kết quả
      this.cache.set(cacheKey, {
        data: timeframeStats,
        timestamp: Date.now()
      });

      return timeframeStats;
    } catch (error) {
      logger.logError('Error getting timeframe statistics:', error.message);
      return {};
    }
  }

  /**
   * Lấy thống kê theo symbol
   */
  async getSymbolStatistics(days = 30, limit = 10) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const pipeline = [
        {
          $match: {
            createdAt: { $gte: startDate },
            status: { $in: ['hit_tp', 'hit_sl'] },
            isConflictNotification: false
          }
        },
        {
          $group: {
            _id: '$symbol',
            signals: { $push: '$$ROOT' }
          }
        },
        {
          $limit: limit
        }
      ];

      const results = await TradingSignal.aggregate(pipeline);
      const symbolStats = {};

      for (const result of results) {
        const symbol = result._id;
        const signals = result.signals;
        symbolStats[symbol] = this.calculateStatistics(signals);
      }

      return symbolStats;
    } catch (error) {
      logger.logError('Error getting symbol statistics:', error.message);
      return {};
    }
  }

  /**
   * Tính toán thống kê từ danh sách signals
   */
  calculateStatistics(signals) {
    if (!signals || signals.length === 0) {
      return this.getEmptyStats();
    }

    let winTrades = 0;
    let lossTrades = 0;
    let totalPnL = 0;
    let winPnL = 0;
    let lossPnL = 0;
    let maxWin = 0;
    let maxLoss = 0;

    for (const signal of signals) {
      const result = this.calculateWinLoss(signal);
      const pnl = signal.pnlPercent || 0;

      totalPnL += pnl;

      if (result === 'WIN') {
        winTrades++;
        winPnL += pnl;
        maxWin = Math.max(maxWin, pnl);
      } else {
        lossTrades++;
        lossPnL += pnl;
        maxLoss = Math.min(maxLoss, pnl);
      }
    }

    const totalTrades = winTrades + lossTrades;
    const winRate = totalTrades > 0 ? (winTrades / totalTrades) * 100 : 0;
    const avgPnL = totalTrades > 0 ? totalPnL / totalTrades : 0;
    const avgWin = winTrades > 0 ? winPnL / winTrades : 0;
    const avgLoss = lossTrades > 0 ? lossPnL / lossTrades : 0;

    return {
      totalTrades,
      winTrades,
      lossTrades,
      winRate,
      totalPnL,
      avgPnL,
      avgWin,
      avgLoss,
      maxWin,
      maxLoss,
      profitFactor: Math.abs(avgLoss) > 0 ? avgWin / Math.abs(avgLoss) : 0
    };
  }

  /**
   * Lấy thống kê rỗng
   */
  getEmptyStats() {
    return {
      totalTrades: 0,
      winTrades: 0,
      lossTrades: 0,
      winRate: 0,
      totalPnL: 0,
      avgPnL: 0,
      avgWin: 0,
      avgLoss: 0,
      maxWin: 0,
      maxLoss: 0,
      profitFactor: 0
    };
  }

  /**
   * Tạo báo cáo định kỳ
   */
  async generateDailyReport() {
    try {
      const overallStats = await this.getOverallStatistics(30);
      const timeframeStats = await this.getTimeframeStatistics(30);
      const symbolStats = await this.getSymbolStatistics(7, 5); // Top 5 symbols trong 7 ngày

      return {
        overall: overallStats,
        timeframes: timeframeStats,
        topSymbols: symbolStats,
        generatedAt: new Date()
      };
    } catch (error) {
      logger.logError('Error generating daily report:', error.message);
      return null;
    }
  }

  /**
   * Xóa cache
   */
  clearCache() {
    this.cache.clear();
    logger.logInfo('Statistics cache cleared');
  }

  /**
   * Cập nhật PnL cho signal
   */
  async updateSignalPnL(signalId, exitPrice) {
    try {
      const signal = await TradingSignal.findById(signalId);
      if (!signal) {
        return false;
      }

      const pnlPercent = signal.calculatePnL(exitPrice);
      
      await TradingSignal.findByIdAndUpdate(signalId, {
        exitPrice,
        pnlPercent,
        exitTime: new Date()
      });

      // Xóa cache để force refresh
      this.clearCache();

      logger.logInfo(`Updated PnL for signal ${signalId}: ${pnlPercent.toFixed(2)}%`);
      return true;
    } catch (error) {
      logger.logError('Error updating signal PnL:', error.message);
      return false;
    }
  }
}

module.exports = new StatisticsService();
