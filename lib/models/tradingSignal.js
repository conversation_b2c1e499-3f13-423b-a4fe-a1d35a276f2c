const mongoose = require('mongoose');

const tradingSignalSchema = new mongoose.Schema({
  symbol: {
    type: String,
    required: true,
    index: true
  },
  timeframe: {
    type: String,
    required: true,
    enum: ['1m', '5m', '15m']
  },
  type: {
    type: String,
    required: true,
    enum: ['BUY', 'SELL']
  },
  entry: {
    type: Number,
    required: true
  },
  stopLoss: {
    type: Number,
    required: true
  },
  takeProfit: {
    type: Number,
    required: true
  },
  riskReward: {
    type: Number,
    default: 0
  },
  tpMethod: {
    type: String,
    default: 'percentage'
  },
  atr: {
    type: Number,
    default: null
  },
  riskAmount: {
    type: Number,
    default: 0
  },
  rewardAmount: {
    type: Number,
    default: 0
  },
  indicators: {
    ema50: Number,
    ema200: Number,
    macd: {
      macd: Number,
      signal: Number,
      histogram: Number
    },
    rsi: Number,
    engulfing: {
      type: String,
      enum: ['bullish', 'bearish']
    }
  },
  marketData: {
    open: Number,
    high: Number,
    low: Number,
    close: Number,
    volume: Number
  },
  status: {
    type: String,
    enum: ['active', 'hit_tp', 'hit_sl', 'cancelled'],
    default: 'active'
  },
  exitPrice: Number,
  exitTime: Date,
  pnlPercent: Number,
  telegramMessageId: String,
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Index cho tìm kiếm nhanh
tradingSignalSchema.index({ symbol: 1, status: 1 });
tradingSignalSchema.index({ createdAt: -1 });
tradingSignalSchema.index({ status: 1, updatedAt: -1 });

// Middleware để tự động cập nhật updatedAt
tradingSignalSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Methods
tradingSignalSchema.methods.calculatePnL = function(currentPrice) {
  if (this.type === 'BUY') {
    return ((currentPrice - this.entry) / this.entry) * 100;
  } else {
    return ((this.entry - currentPrice) / this.entry) * 100;
  }
};

tradingSignalSchema.methods.checkStopLoss = function(currentPrice) {
  if (this.type === 'BUY') {
    return currentPrice <= this.stopLoss;
  } else {
    return currentPrice >= this.stopLoss;
  }
};

tradingSignalSchema.methods.checkTakeProfit = function(currentPrice) {
  if (this.type === 'BUY') {
    return currentPrice >= this.takeProfit;
  } else {
    return currentPrice <= this.takeProfit;
  }
};

// Statics
tradingSignalSchema.statics.getActiveSignals = function() {
  return this.find({ status: 'active' });
};

tradingSignalSchema.statics.getStatistics = function(days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  return this.aggregate([
    {
      $match: {
        createdAt: { $gte: startDate },
        status: { $in: ['hit_tp', 'hit_sl'] }
      }
    },
    {
      $group: {
        _id: null,
        totalTrades: { $sum: 1 },
        winTrades: {
          $sum: {
            $cond: [{ $eq: ['$status', 'hit_tp'] }, 1, 0]
          }
        },
        lossTrades: {
          $sum: {
            $cond: [{ $eq: ['$status', 'hit_sl'] }, 1, 0]
          }
        },
        totalPnL: { $sum: '$pnlPercent' },
        avgPnL: { $avg: '$pnlPercent' }
      }
    },
    {
      $project: {
        _id: 0,
        totalTrades: 1,
        winTrades: 1,
        lossTrades: 1,
        winRate: {
          $multiply: [
            { $divide: ['$winTrades', '$totalTrades'] },
            100
          ]
        },
        totalPnL: { $round: ['$totalPnL', 2] },
        avgPnL: { $round: ['$avgPnL', 2] }
      }
    }
  ]);
};

module.exports = mongoose.model('TradingSignal', tradingSignalSchema);
