const mongoose = require('mongoose');

const marketDataSchema = new mongoose.Schema({
  symbol: {
    type: String,
    required: true,
    index: true
  },
  timeframe: {
    type: String,
    required: true,
    enum: ['1m', '5m', '15m']
  },
  openTime: {
    type: Date,
    required: true,
    index: true
  },
  closeTime: {
    type: Date,
    required: true
  },
  open: {
    type: Number,
    required: true
  },
  high: {
    type: Number,
    required: true
  },
  low: {
    type: Number,
    required: true
  },
  close: {
    type: Number,
    required: true
  },
  volume: {
    type: Number,
    required: true
  },
  quoteVolume: {
    type: Number,
    required: true
  },
  trades: {
    type: Number,
    required: true
  },
  indicators: {
    ema50: Number,
    ema200: Number,
    macd: {
      macd: Number,
      signal: Number,
      histogram: Number
    },
    rsi: Number
  },
  patterns: {
    engulfing: {
      type: String,
      enum: ['bullish', 'bearish', 'none'],
      default: 'none'
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Compound indexes
marketDataSchema.index({ symbol: 1, timeframe: 1, openTime: -1 });
marketDataSchema.index({ symbol: 1, timeframe: 1, closeTime: -1 });

// Statics
marketDataSchema.statics.getLatestCandles = function(symbol, timeframe, limit = 1000) {
  return this.find({
    symbol,
    timeframe
  })
  .sort({ openTime: -1 })
  .limit(limit);
};

marketDataSchema.statics.getHistoricalData = function(symbol, timeframe, startTime, endTime) {
  return this.find({
    symbol,
    timeframe,
    openTime: { $gte: startTime, $lte: endTime }
  }).sort({ openTime: 1 });
};

module.exports = mongoose.model('MarketData', marketDataSchema);
